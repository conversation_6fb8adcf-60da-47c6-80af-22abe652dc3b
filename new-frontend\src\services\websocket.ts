// WebSocket 连接管理服务 - 通用多功能版本
// 支持重量数据、车牌识别、设备状态、系统通知等多种消息类型
// 作者：仕伟

import { ref, computed, readonly, onMounted, onUnmounted } from 'vue'

// WebSocket消息类型定义 - 根据真实后端协议更新
export type MessageType = 
  // 重量相关（支持大小写）
  | 'weight_data' | 'WEIGHT_DATA' | 'connection_status' | 'error_message' | 'heartbeat'
  // RG设备相关
  | 'rg_vehicle_recognition' | 'rg_snapshot_result' | 'rg_device_status' 
  | 'rg_traffic_statistics' | 'rg_command_reply'
  // 兼容旧版本
  | 'plate_recognition' | 'PLATE_RECOGNITION' | 'camera_status' | 'device_status' | 'system_notification'

export interface WebSocketMessage {
  type: MessageType
  timestamp: string
  data: any
}

// ===== 重量数据相关接口 =====
export interface WeightData {
  weight: number
  unit: string
  status: 'stable' | 'unstable' | 'error' | 'offline' | 'normal'
  hasData: boolean
  message: string
}

export interface ConnectionStatusData {
  status: 'connected' | 'disconnected' | 'error'
  message: string
  hasData: boolean
}

export interface ErrorMessageData {
  status: 'error'
  message: string
  hasData: boolean
}

export interface HeartbeatData {
  status: 'normal'
  message: string
  hasData?: boolean
}

// ===== RG设备相关接口 =====
export interface RgVehicleRecognitionData {
  plateNumber: string
  eventType: number
  eventDescription: string
  eventTime: string
  decodedPlateNumber: string
  isBusinessEvent: boolean
}

export interface RgVehicleRecognitionMessage {
  type: 'rg_vehicle_recognition'
  timestamp: string
  deviceSn: string
  data: RgVehicleRecognitionData
  meta: {
    version: string
    source: string
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    category: 'BUSINESS' | 'SYSTEM'
  }
}

export interface RgSnapshotResultData {
  success: boolean
  stateCode: string
  imageFormat: string
  imagePath: string
  imageDataUrl?: string
  snapshotTime: string
}

export interface RgDeviceStatusData {
  online: boolean
  changeType: string
  description: string
  onlineDuration: number
  connectionStability: 'STABLE' | 'UNSTABLE' | 'POOR'
}

export interface RgTrafficStatisticsData {
  atobCount: number      // A到B方向车流量
  btoaCount: number      // B到A方向车流量  
  totalCount: number
  density: '正常' | '密集' | '拥堵' | '稀疏'
  timeWindow: string
  flowRate: number       // 车流速率(辆/分钟)
}

export interface RgCommandReplyData {
  commandMessageId: string
  commandName: string
  success: boolean
  response: string
  responseTime: number   // 响应时间(ms)
  replyTime: string
}

// ===== 车牌识别相关接口 =====
export interface PlateRecognitionData {
  plateNumber: string
  confidence: number
  imageUrl: string
  timestamp: number
  cameraId: string
}

export interface CameraStatusData {
  cameraId: string
  status: 'online' | 'offline' | 'error'
  message: string
}

// ===== 设备状态相关接口 =====
export interface DeviceStatusData {
  deviceType: 'camera' | 'scale' | 'gate' | 'mqtt'
  deviceId: string
  status: 'online' | 'offline' | 'error'
  message: string
  details?: any
}

// ===== 系统通知相关接口 =====
export interface SystemNotificationData {
  level: 'info' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  autoClose?: boolean
}

export interface WebSocketConfig {
  url: string
  reconnectInterval: number
  maxReconnectAttempts: number
}

export class WebSocketService {
  private connections = new Map<string, {
    ws: WebSocket | null
    isConnected: boolean
    reconnectAttempts: number
    reconnectTimer: number | null
    heartbeatTimer: number | null
  }>()
  
  private config: WebSocketConfig
  private messageHandlers = new Map<MessageType, Array<(data: any) => void>>()
  private globalHandlers = new Array<(type: MessageType, data: any) => void>()

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: 'ws://localhost:8077/ws', // 基础WebSocket地址
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      ...config
    }
  }

  // 连接指定端点的WebSocket
  connect(endpoint: string = '/weight'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const fullUrl = `${this.config.url}${endpoint}`
        console.log('尝试连接WebSocket端点:', fullUrl)
        
        // 如果连接已存在，先关闭
        if (this.connections.has(endpoint)) {
          this.disconnect(endpoint)
        }
        
        // 创建新的连接状态
        const connection = {
          ws: null as WebSocket | null,
          isConnected: false,
          reconnectAttempts: 0,
          reconnectTimer: null as number | null,
          heartbeatTimer: null as number | null
        }
        
        // 创建WebSocket连接
        connection.ws = new WebSocket(fullUrl)

        connection.ws.onopen = () => {
          connection.isConnected = true
          connection.reconnectAttempts = 0
          console.log(`WebSocket端点${endpoint}连接成功`)
          
          // 启动心跳检测
          this.startHeartbeat(endpoint)
          
          resolve()
        }

        connection.ws.onmessage = (event) => {
          this.handleMessage(event.data, endpoint)
        }

        connection.ws.onclose = (event) => {
          connection.isConnected = false
          this.stopHeartbeat(endpoint)
          console.log(`WebSocket端点${endpoint}连接关闭, code:`, event.code, 'reason:', event.reason)
          
          // 只有在非正常关闭时才尝试重连
          if (event.code !== 1000) {
            this.attemptReconnect(endpoint)
          }
        }

        connection.ws.onerror = (error) => {
          console.error(`WebSocket端点${endpoint}连接错误:`, error)
          reject(error)
        }
        
        // 保存连接
        this.connections.set(endpoint, connection)
        
      } catch (error) {
        console.error(`WebSocket端点${endpoint}连接失败:`, error)
        reject(error)
      }
    })
  }

  // 连接多个端点
  async connectMultiple(endpoints: string[]): Promise<void> {
    const promises = endpoints.map(endpoint => this.connect(endpoint))
    await Promise.allSettled(promises)
  }

  // 断开指定端点连接
  disconnect(endpoint?: string) {
    if (endpoint) {
      // 断开指定端点
      const connection = this.connections.get(endpoint)
      if (connection) {
        if (connection.ws) {
          connection.ws.close(1000, '用户主动断开')
        }
        this.stopHeartbeat(endpoint)
        if (connection.reconnectTimer) {
          clearTimeout(connection.reconnectTimer)
        }
        this.connections.delete(endpoint)
        console.log(`WebSocket端点${endpoint}已断开连接`)
      }
    } else {
      // 断开所有连接
      this.connections.forEach((connection, ep) => {
        if (connection.ws) {
          connection.ws.close(1000, '用户主动断开所有连接')
        }
        this.stopHeartbeat(ep)
        if (connection.reconnectTimer) {
          clearTimeout(connection.reconnectTimer)
        }
      })
      this.connections.clear()
      console.log('所有WebSocket连接已断开')
    }
  }

  // 发送消息到指定端点
  send(message: string, endpoint: string = '/weight') {
    const connection = this.connections.get(endpoint)
    if (connection && connection.isConnected && connection.ws) {
      connection.ws.send(message)
    } else {
      console.warn(`WebSocket端点${endpoint}未连接，无法发送消息`)
    }
  }

  // 广播消息到所有连接
  broadcast(message: string) {
    this.connections.forEach((connection, endpoint) => {
      if (connection.isConnected && connection.ws) {
        connection.ws.send(message)
      }
    })
  }

  // 发送心跳消息到指定端点
  sendHeartbeat(endpoint: string = '/weight') {
    const connection = this.connections.get(endpoint)
    if (connection && connection.isConnected) {
      this.send('ping', endpoint)
    }
  }

  // 启动心跳检测
  private startHeartbeat(endpoint: string) {
    this.stopHeartbeat(endpoint)
    const connection = this.connections.get(endpoint)
    if (connection) {
      connection.heartbeatTimer = window.setInterval(() => {
        this.sendHeartbeat(endpoint)
      }, 30000) // 每30秒发送一次心跳
    }
  }

  // 停止心跳检测
  private stopHeartbeat(endpoint: string) {
    const connection = this.connections.get(endpoint)
    if (connection && connection.heartbeatTimer) {
      clearInterval(connection.heartbeatTimer)
      connection.heartbeatTimer = null
    }
  }

  // 订阅消息类型
  subscribe(type: MessageType, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  // 取消订阅
  unsubscribe(type: MessageType, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }
  }

  // 处理收到的消息
  private handleMessage(data: string, endpoint: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      console.log(`收到WebSocket消息 [${endpoint}]:`, message)
      
      // 触发全局消息处理器
      this.globalHandlers.forEach(handler => {
        try {
          handler(message.type, message.data)
        } catch (error) {
          console.error('全局消息处理器执行失败:', error)
        }
      })
      
      // 触发对应类型的消息处理器
      const handlers = this.messageHandlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message.data)
          } catch (error) {
            console.error('消息处理器执行失败:', error)
          }
        })
      }
    } catch (error) {
      console.error(`解析WebSocket消息失败 [${endpoint}]:`, error, 'raw data:', data)
    }
  }

  // 添加全局消息处理器
  addGlobalHandler(handler: (type: MessageType, data: any) => void) {
    this.globalHandlers.push(handler)
  }

  // 移除全局消息处理器
  removeGlobalHandler(handler: (type: MessageType, data: any) => void) {
    const index = this.globalHandlers.indexOf(handler)
    if (index !== -1) {
      this.globalHandlers.splice(index, 1)
    }
  }

  // 尝试重连指定端点
  private attemptReconnect(endpoint: string) {
    const connection = this.connections.get(endpoint)
    if (!connection) return

    if (connection.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error(`WebSocket端点${endpoint}重连次数已达上限，停止重连`)
      return
    }

    connection.reconnectAttempts++
    console.log(`WebSocket端点${endpoint}重连尝试 ${connection.reconnectAttempts}/${this.config.maxReconnectAttempts}`)

    connection.reconnectTimer = window.setTimeout(() => {
      this.connect(endpoint).catch(() => {
        // 重连失败，继续尝试
        this.attemptReconnect(endpoint)
      })
    }, this.config.reconnectInterval)
  }

  // 获取指定端点连接状态
  isConnected(endpoint: string = '/weight'): boolean {
    const connection = this.connections.get(endpoint)
    return connection ? connection.isConnected : false
  }

  // 获取指定端点重连次数
  getReconnectAttempts(endpoint: string = '/weight'): number {
    const connection = this.connections.get(endpoint)
    return connection ? connection.reconnectAttempts : 0
  }

  // 获取最大重连次数
  getMaxReconnectAttempts(): number {
    return this.config.maxReconnectAttempts
  }

  // 获取所有连接状态
  getAllConnectionStatus(): Map<string, boolean> {
    const status = new Map<string, boolean>()
    this.connections.forEach((connection, endpoint) => {
      status.set(endpoint, connection.isConnected)
    })
    return status
  }
}

// 创建全局 WebSocket 服务实例
export const wsService = new WebSocketService()

// 通用WebSocket组合式函数
export function useWebSocket() {
  const connections = ref<Map<string, boolean>>(new Map())
  
  // 更新所有连接状态
  const updateConnectionStatus = () => {
    connections.value = wsService.getAllConnectionStatus()
  }
  
  // 连接指定端点
  const connect = async (endpoint: string = '/weight') => {
    try {
      await wsService.connect(endpoint)
      updateConnectionStatus()
    } catch (error) {
      console.error(`WebSocket端点${endpoint}连接失败:`, error)
      throw error
    }
  }
  
  // 连接多个端点
  const connectMultiple = async (endpoints: string[]) => {
    await wsService.connectMultiple(endpoints)
    updateConnectionStatus()
  }
  
  // 断开连接
  const disconnect = (endpoint?: string) => {
    wsService.disconnect(endpoint)
    updateConnectionStatus()
  }
  
  // 检查指定端点是否连接
  const isConnected = (endpoint: string = '/weight') => {
    return wsService.isConnected(endpoint)
  }
  
  // 订阅消息类型
  const subscribe = (type: MessageType, handler: (data: any) => void) => {
    wsService.subscribe(type, handler)
  }
  
  // 取消订阅
  const unsubscribe = (type: MessageType, handler: (data: any) => void) => {
    wsService.unsubscribe(type, handler)
  }
  
  // 发送消息
  const send = (message: string, endpoint: string = '/weight') => {
    wsService.send(message, endpoint)
  }
  
  // 广播消息
  const broadcast = (message: string) => {
    wsService.broadcast(message)
  }
  
  // 添加全局消息处理器
  const addGlobalHandler = (handler: (type: MessageType, data: any) => void) => {
    wsService.addGlobalHandler(handler)
  }
  
  // 定期更新连接状态
  onMounted(() => {
    const timer = setInterval(updateConnectionStatus, 1000)
    onUnmounted(() => clearInterval(timer))
  })
  
  return {
    // 状态
    connections: readonly(connections),
    
    // 方法
    connect,
    connectMultiple,
    disconnect,
    isConnected,
    subscribe,
    unsubscribe,
    send,
    broadcast,
    addGlobalHandler
  }
}

// 专门用于重量数据的组合式函数（向后兼容）
export function useWeightWebSocket() {
  const { connect, disconnect, subscribe, unsubscribe, send, isConnected } = useWebSocket()
  
  const weightEndpoint = '/weight'
  const connectionStatus = ref<'connecting' | 'connected' | 'disconnected' | 'reconnecting'>('disconnected')
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(10)
  
  // 更新重量端点状态
  const updateConnectionStatus = () => {
    const connected = isConnected(weightEndpoint)
    const attempts = wsService.getReconnectAttempts(weightEndpoint)
    
    if (connected) {
      connectionStatus.value = 'connected'
    } else if (attempts > 0) {
      connectionStatus.value = 'reconnecting'
    } else {
      connectionStatus.value = 'disconnected'
    }
    
    reconnectAttempts.value = attempts
    maxReconnectAttempts.value = wsService.getMaxReconnectAttempts()
  }
  
  // 连接重量数据端点
  const connectWeight = async () => {
    try {
      connectionStatus.value = 'connecting'
      await connect(weightEndpoint)
      updateConnectionStatus()
    } catch (error) {
      connectionStatus.value = 'disconnected'
      throw error
    }
  }
  
  // 断开重量数据连接
  const disconnectWeight = () => {
    disconnect(weightEndpoint)
    connectionStatus.value = 'disconnected'
    updateConnectionStatus()
  }
  
  // 自动连接重量数据端点
  const autoConnect = async () => {
    if (!isConnected(weightEndpoint)) {
      try {
        await connectWeight()
        console.log('重量数据WebSocket自动连接成功')
      } catch (error) {
        console.error('重量数据WebSocket自动连接失败:', error)
      }
    }
  }
  
  // 重量数据相关的订阅方法
  const subscribeWeightData = (handler: (data: WeightData) => void) => {
    subscribe('weight_data', handler)
    subscribe('WEIGHT_DATA', handler) // 兼容后端大写消息类型
  }
  
  const subscribeConnectionStatus = (handler: (data: ConnectionStatusData) => void) => {
    subscribe('connection_status', handler)
  }
  
  const subscribeErrorMessage = (handler: (data: ErrorMessageData) => void) => {
    subscribe('error_message', handler)
  }
  
  const subscribeHeartbeat = (handler: (data: HeartbeatData) => void) => {
    subscribe('heartbeat', handler)
  }

  // RG设备相关的订阅方法
  const subscribeRgVehicleRecognition = (handler: (data: RgVehicleRecognitionMessage) => void) => {
    subscribe('rg_vehicle_recognition', handler)
  }

  // 车牌识别订阅方法（兼容大小写）
  const subscribePlateRecognition = (handler: (data: PlateRecognitionData) => void) => {
    subscribe('plate_recognition', handler)
    subscribe('PLATE_RECOGNITION', handler) // 兼容后端大写消息类型
  }

  const subscribeRgSnapshotResult = (handler: (data: { deviceSn: string, data: RgSnapshotResultData }) => void) => {
    subscribe('rg_snapshot_result', handler)
  }

  const subscribeRgDeviceStatus = (handler: (data: { deviceSn: string, data: RgDeviceStatusData }) => void) => {
    subscribe('rg_device_status', handler)
  }

  const subscribeRgTrafficStatistics = (handler: (data: { deviceSn: string, data: RgTrafficStatisticsData }) => void) => {
    subscribe('rg_traffic_statistics', handler)
  }

  const subscribeRgCommandReply = (handler: (data: { deviceSn: string, data: RgCommandReplyData }) => void) => {
    subscribe('rg_command_reply', handler)
  }
  
  const sendHeartbeat = () => {
    send('ping', weightEndpoint)
  }
  
  // 定期更新状态
  onMounted(() => {
    const timer = setInterval(updateConnectionStatus, 1000)
    onUnmounted(() => clearInterval(timer))
  })
  
  return {
    // 状态
    isConnected: computed(() => isConnected(weightEndpoint)),
    connectionStatus: readonly(connectionStatus),
    reconnectAttempts: readonly(reconnectAttempts),
    maxReconnectAttempts: readonly(maxReconnectAttempts),
    
    // 方法
    connect: connectWeight,
    disconnect: disconnectWeight,
    autoConnect,
    // 重量数据订阅
    subscribeWeightData,
    subscribeConnectionStatus,
    subscribeErrorMessage,
    subscribeHeartbeat,
    // 车牌识别订阅
    subscribePlateRecognition,
    // RG设备数据订阅
    subscribeRgVehicleRecognition,
    subscribeRgSnapshotResult,
    subscribeRgDeviceStatus,
    subscribeRgTrafficStatistics,
    subscribeRgCommandReply,
    // 通用方法
    unsubscribe,
    sendHeartbeat
  }
}