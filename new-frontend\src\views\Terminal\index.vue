<template>
  <div class="terminal-system" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 系统状态栏 -->
    <div class="system-statusbar">
      <div class="status-left">
        <div class="system-logo">
          <img src="/logo.svg" alt="系统Logo" />
          <span class="system-title">河北省采砂监管一体机</span>
        </div>
        <div class="device-status">
          <StatusIndicator 
            v-for="device in deviceList"
            :key="device.name"
            :status="device.status"
            :label="device.label"
            :icon="device.icon"
            size="small"
          />
        </div>
      </div>
      
      <div class="status-right">
        <div class="system-info">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date">{{ currentDate }}</div>
        </div>
        <div class="system-controls">
          <ThemeToggle />
          <el-button 
            icon="Setting" 
            circle 
            size="small"
            @click="showSettings = true"
          />
        </div>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="terminal-workspace">
      <!-- 左侧监控区域 -->
      <div class="left-panel">
        <!-- 主视频监控 -->
        <div class="dual-monitor">
          <div class="monitor-header">
            <h3>双摄像头监控</h3>
            <div class="monitor-controls">
              <el-button size="small" @click="toggleAllCameras">
                {{ allCamerasPlaying ? '停止全部' : '开始全部' }}
              </el-button>
              <el-button size="small" @click="captureAllPhotos">
                <el-icon><Camera /></el-icon>
                全部抓拍
              </el-button>
            </div>
          </div>
          <div class="video-container">
            <div class="video-player" :class="{ playing: videoPlaying }">
              <div v-if="!videoPlaying" class="video-placeholder">
                <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
                <p>视频监控</p>
                <el-button type="primary" @click="toggleVideo">开始监控</el-button>
              </div>
              <div v-else class="video-stream">
                <video class="video-element" autoplay muted></video>
                <div class="video-overlay">
                  <div class="recording-indicator">
                    <div class="rec-dot"></div>
                    <span>REC</span>
                  </div>
                  <div class="video-info">
                    <div>1920x1080</div>
                    <div>25fps</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 称重显示 -->
        <div class="weight-section">
          <div class="weight-display">
            <div class="weight-header">
              <h4>实时称重</h4>
              <el-tag :type="weightStatus.type" size="small">{{ weightStatus.text }}</el-tag>
            </div>
            <div class="weight-value" :class="weightClass">
              <span class="number">{{ formatWeight(currentWeight) }}</span>
              <span class="unit">吨</span>
            </div>
            <div class="weight-actions">
              <el-button size="small" @click="tareWeight">调零</el-button>
              <el-button size="small" type="primary" @click="confirmWeight" :disabled="!weightStable">
                确认重量
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="right-panel">
        <!-- 车牌输入区 -->
        <div class="plate-section">
          <div class="plate-header">
            <h4>车牌识别</h4>
            <el-switch v-model="autoRecognition" active-text="自动" inactive-text="手动" />
          </div>
          
          <!-- 车牌显示 -->
          <div class="plate-display">
            <div class="plate-number" :class="plateValidClass">
              <span class="province">{{ plateData.province || '省' }}</span>
              <span class="numbers">{{ plateData.numbers || 'A12345' }}</span>
            </div>
            <div class="plate-status">
              <el-icon :class="plateValidClass">
                <CircleCheckFilled v-if="plateValid" />
                <CircleCloseFilled v-else />
              </el-icon>
              <span>{{ plateStatusText }}</span>
            </div>
          </div>

          <!-- 手动输入键盘 -->
          <div v-show="!autoRecognition" class="virtual-keyboard">
            <div class="province-select">
              <el-button 
                v-for="province in provinces.slice(0, 8)"
                :key="province"
                size="small"
                :type="plateData.province === province ? 'primary' : 'default'"
                @click="selectProvince(province)"
              >
                {{ province }}
              </el-button>
            </div>
            <div class="keyboard-grid">
              <el-button 
                v-for="char in keyboardChars"
                :key="char"
                size="small"
                @click="inputChar(char)"
              >
                {{ char }}
              </el-button>
              <el-button size="small" type="danger" @click="deleteLast">删除</el-button>
              <el-button size="small" type="info" @click="clearPlate">清空</el-button>
            </div>
          </div>
        </div>

        <!-- 操作控制区 -->
        <div class="control-section">
          <div class="vehicle-status">
            <h4>车辆状态</h4>
            <el-radio-group v-model="vehicleStatus" size="large">
              <el-radio-button value="IN">入场</el-radio-button>
              <el-radio-button value="OUT">出场</el-radio-button>
            </el-radio-group>
          </div>

          <div class="main-actions">
            <el-button 
              type="success" 
              size="large" 
              :disabled="!canSubmit"
              @click="submitData"
              :loading="submitting"
            >
              <el-icon><Upload /></el-icon>
              {{ submitting ? '提交中...' : '提交数据' }}
            </el-button>
            
            <el-button 
              type="primary" 
              size="large"
              :disabled="!canTriggerGate"
              @click="triggerGate"
              :loading="gateTriggering"
            >
              <el-icon><Unlock /></el-icon>
              {{ gateTriggering ? '抬杆中...' : '抬杆放行' }}
            </el-button>

            <el-button 
              type="warning" 
              size="large"
              @click="resetSystem"
            >
              <el-icon><RefreshLeft /></el-icon>
              重置系统
            </el-button>
          </div>
        </div>

        <!-- 最近记录 - 已隐藏 -->
        <!-- <div class="recent-records" style="display: none;">
          <div class="records-header">
            <h4>最近记录</h4>
            <el-link @click="showHistoryDialog = true" type="primary">查看更多</el-link>
          </div>
          <div class="records-list">
            <div 
              v-for="record in recentRecords"
              :key="record.id"
              class="record-item"
            >
              <el-tag size="small" :type="record.status === 'IN' ? 'success' : 'warning'">
                {{ record.plateNumber }}
              </el-tag>
              <span class="record-weight">{{ record.weight }}吨</span>
              <span class="record-time">{{ formatRecordTime(record.timestamp) }}</span>
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 底部快捷操作栏 -->
    <div class="bottom-toolbar">
      <div class="toolbar-left">
        <div class="system-status">
          <el-tag :type="systemStatusType" size="small">{{ systemStatusText }}</el-tag>
          <span class="status-details">{{ statusDetails }}</span>
        </div>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="showHistoryDialog = true">
          <el-icon><Document /></el-icon>
          历史记录
        </el-button>
        <!-- 数据导出按钮 - 已隐藏 -->
        <!-- <el-button size="small" @click="exportData">
          <el-icon><Download /></el-icon>
          数据导出
        </el-button> -->
        <el-button size="small" @click="showSettings = true">
          <el-icon><Tools /></el-icon>
          系统设置
        </el-button>
      </div>
    </div>

    <!-- 设置对话框 -->
    <SettingsDialog v-model="showSettings" />
    
    <!-- 历史记录对话框 -->
    <HistoryDialog v-model="showHistoryDialog" />
  </div>
</template>

<script setup lang="ts">
// 一体机终端系统主界面
// 作者：仕伟

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import StatusIndicator from '@/components/StatusIndicator.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import SettingsDialog from '@/components/SettingsDialog.vue'
import HistoryDialog from '@/components/HistoryDialog.vue'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/services/websocket'
import dayjs from 'dayjs'
import { 
  VideoCamera, 
  Camera,
  Upload, 
  Unlock, 
  RefreshLeft, 
  Document, 
  Download, 
  Tools,
  View,
  Delete,
  Close,
  Picture,
  SuccessFilled,
  WarningFilled,
  CircleCheckFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'

// Store
const deviceStore = useDeviceStore()

// WebSocket
const { connect, subscribe, isConnected } = useWebSocket()

// 强制性全屏模式 - 始终启用内置全屏
const isFullscreen = ref(true) // 强制设为true，不可切换

// 设备列表 - 地磅状态根据WebSocket连接状态变化
const deviceList = computed(() => {
  const wsConnected = isConnected('/weight')
  const scaleStatus = wsConnected ? 'online' : 'offline'
  
  return [
    { name: 'camera', label: '摄像头', icon: 'VideoCamera', status: deviceStore.status.camera },
    { name: 'scale', label: '地磅', icon: 'Scale', status: scaleStatus },
    { name: 'gate', label: '道闸', icon: 'Operation', status: deviceStore.status.gate }
  ]
})

// 系统时间
const currentTime = ref('')
const currentDate = ref('')

// 视频状态
const videoPlaying = ref(false)

// 称重数据
const currentWeight = ref(0)
const weightStable = ref(true)

// 车牌数据
const plateData = reactive({ province: '', numbers: '' })
const plateValid = ref(false)
const autoRecognition = ref(true)

// 车辆状态
const vehicleStatus = ref<'IN' | 'OUT'>('IN')

// 操作状态
const submitting = ref(false)
const gateTriggering = ref(false)

// 对话框状态
const showSettings = ref(false)
const showHistoryDialog = ref(false)

// 最近记录
const recentRecords = ref([])

// 省份列表
const provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁']

// 键盘字符
const keyboardChars = ['A','B','C','D','E','F','G','H','J','K','L','M','N','P','Q','R','S','T','U','V','W','X','Y','Z','0','1','2','3','4','5','6','7','8','9']

// 计算属性
const plateNumber = computed(() => `${plateData.province}${plateData.numbers}`)

const plateValidClass = computed(() => plateValid.value ? 'valid' : 'invalid')

const plateStatusText = computed(() => {
  if (!plateNumber.value || plateNumber.value.length < 7) {
    // 支持7位民用车牌和8位新能源车牌
    return '请输入完整车牌（7-8位）'
  }
  return plateValid.value ? '车牌有效' : '车牌无效'
})

const weightClass = computed(() => {
  if (currentWeight.value > 50) return 'overload'
  if (currentWeight.value < 5) return 'light'
  return 'normal'
})

const weightStatus = computed(() => {
  const wsConnected = isConnected('/weight')
  
  // 如果WebSocket未连接，显示连接断开状态
  if (!wsConnected) {
    return { type: 'danger', text: '连接断开' }
  }
  
  if (!weightStable.value) return { type: 'warning', text: '不稳定' }
  if (currentWeight.value > 50) return { type: 'danger', text: '超重' }
  return { type: 'success', text: '正常' }
})

const canSubmit = computed(() => {
  return plateValid.value && weightStable.value && currentWeight.value > 0
})

const canTriggerGate = computed(() => canSubmit.value)

const systemStatusType = computed(() => {
  if (deviceStore.overallStatus === 'online') return 'success'
  if (deviceStore.overallStatus === 'warning') return 'warning'
  return 'danger'
})

const systemStatusText = computed(() => {
  const statusMap = {
    online: '系统正常',
    warning: '部分异常', 
    offline: '系统离线',
    error: '系统错误'
  }
  return statusMap[deviceStore.overallStatus] || '未知状态'
})

const statusDetails = computed(() => {
  return `设备在线: ${deviceStore.onlineCount}/${deviceStore.totalCount}`
})

// 方法
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY年MM月DD日 dddd')
}

const formatWeight = (weight: number) => weight.toFixed(1)

const formatRecordTime = (timestamp: number) => {
  return dayjs(timestamp).format('HH:mm')
}

const toggleVideo = () => {
  videoPlaying.value = !videoPlaying.value
  ElMessage.success(videoPlaying.value ? '开始视频监控' : '停止视频监控')
}

const capturePhoto = () => {
  ElMessage.success('抓拍成功')
}

const tareWeight = () => {
  currentWeight.value = 0
  ElMessage.success('调零完成')
}

const confirmWeight = () => {
  ElMessage.success(`已确认重量: ${formatWeight(currentWeight.value)}吨`)
}

const selectProvince = (province: string) => {
  plateData.province = province
  validatePlate()
}

const inputChar = (char: string) => {
  if (plateData.numbers.length < 6) {
    plateData.numbers += char
    validatePlate()
  }
}

const deleteLast = () => {
  if (plateData.numbers.length > 0) {
    plateData.numbers = plateData.numbers.slice(0, -1)
    validatePlate()
  }
}

const clearPlate = () => {
  plateData.province = ''
  plateData.numbers = ''
  plateValid.value = false
}

const validatePlate = () => {
  const length = plateNumber.value.length
  // 支持7位民用车牌和8位新能源车牌
  if (length >= 7 && length <= 8) {
    // 模拟验证逻辑
    plateValid.value = Math.random() > 0.2
  } else {
    plateValid.value = false
  }
}

const submitData = async () => {
  submitting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据提交成功')
    
    // 添加到最近记录
    recentRecords.value.unshift({
      id: Date.now(),
      plateNumber: plateNumber.value,
      weight: currentWeight.value,
      status: vehicleStatus.value,
      timestamp: Date.now()
    })
    
    // 重置系统
    resetSystem()
  } catch (error) {
    ElMessage.error('数据提交失败')
  } finally {
    submitting.value = false
  }
}

const triggerGate = async () => {
  gateTriggering.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('抬杆成功，请通行')
  } catch (error) {
    ElMessage.error('抬杆失败')
  } finally {
    gateTriggering.value = false
  }
}

const resetSystem = () => {
  plateData.province = ''
  plateData.numbers = ''
  plateValid.value = false
  currentWeight.value = 0
  ElMessage.info('系统已重置')
}

const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 生命周期
onMounted(async () => {
  // 更新时间
  updateTime()
  setInterval(updateTime, 1000)
  
  // 连接WebSocket
  await connect()
  
  // 订阅数据更新
  subscribe('weight_data', (data) => {
    currentWeight.value = data.weight
    weightStable.value = data.status === 'normal'
  })
  
  subscribe('plate_recognition', (data) => {
    if (autoRecognition.value) {
      const [province, ...numbers] = data.plateNumber.split('')
      plateData.province = province
      plateData.numbers = numbers.join('')
      validatePlate()
    }
  })
  
  // 模拟重量数据变化
  setInterval(() => {
    if (!submitting.value && !gateTriggering.value) {
      currentWeight.value = 20 + Math.random() * 30
      weightStable.value = Math.random() > 0.1
    }
  }, 2000)
  
  // 初始化设备监控
  deviceStore.initDeviceMonitoring()
})
</script>

<style scoped lang="scss">
.terminal-system {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;

  &.fullscreen-mode {
    .system-statusbar {
      display: none;
    }
    
    .terminal-workspace {
      height: calc(100vh - 60px);
    }
  }

  .system-statusbar {
    height: 60px;
    background: var(--bg-header);
    border-bottom: 2px solid var(--el-color-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    box-shadow: var(--shadow-base);

    .status-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-xl);

      .system-logo {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        img {
          width: 36px;
          height: 36px;
        }

        .system-title {
          font-size: 20px;
          font-weight: 700;
          color: var(--text-primary);
          background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .device-status {
        display: flex;
        gap: var(--spacing-md);
      }
    }

    .status-right {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);

      .system-info {
        text-align: right;

        .current-time {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
        }

        .current-date {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }

      .system-controls {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }

  .terminal-workspace {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    overflow: hidden;

    .left-panel, .right-panel {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .left-panel {
      .main-monitor {
        flex: 1;
        background: var(--bg-card);
        border-radius: var(--border-radius-large);
        border: 2px solid var(--border-base);
        display: flex;
        flex-direction: column;

        .monitor-header {
          padding: var(--spacing-md);
          border-bottom: 1px solid var(--border-base);
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: var(--bg-secondary);

          h3 {
            margin: 0;
            color: var(--text-primary);
            font-size: 16px;
          }
        }

        .video-container {
          flex: 1;
          padding: var(--spacing-md);

          .video-player {
            height: 100%;
            background: #000;
            border-radius: var(--border-radius-base);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .video-placeholder {
              text-align: center;
              color: #666;

              .placeholder-icon {
                font-size: 64px;
                margin-bottom: var(--spacing-lg);
              }

              p {
                font-size: 18px;
                margin-bottom: var(--spacing-lg);
              }
            }

            .video-stream {
              width: 100%;
              height: 100%;
              position: relative;

              .video-element {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .video-overlay {
                position: absolute;
                top: var(--spacing-md);
                left: var(--spacing-md);
                right: var(--spacing-md);
                display: flex;
                justify-content: space-between;

                .recording-indicator {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  background: rgba(0,0,0,0.7);
                  color: white;
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border-radius: var(--border-radius-base);
                  font-size: 12px;

                  .rec-dot {
                    width: 8px;
                    height: 8px;
                    background: #ff4d4f;
                    border-radius: 50%;
                    animation: pulse 1.5s infinite;
                  }
                }

                .video-info {
                  background: rgba(0,0,0,0.7);
                  color: white;
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border-radius: var(--border-radius-base);
                  font-size: 10px;
                  text-align: right;
                  
                  div {
                    line-height: 1.2;
                  }
                }
              }
            }
          }
        }
      }

      .weight-section {
        .weight-display {
          background: var(--bg-card);
          padding: var(--spacing-lg);
          border-radius: var(--border-radius-large);
          border: 2px solid var(--border-base);

          .weight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);

            h4 {
              margin: 0;
              color: var(--text-primary);
            }
          }

          .weight-value {
            text-align: center;
            margin-bottom: var(--spacing-lg);

            .number {
              font-size: 48px;
              font-weight: 700;
              font-family: 'Courier New', monospace;
            }

            .unit {
              font-size: 18px;
              margin-left: var(--spacing-sm);
              color: var(--text-secondary);
            }

            &.normal .number {
              color: var(--el-color-primary);
            }

            &.overload .number {
              color: var(--el-color-danger);
              animation: pulse 1.5s infinite;
            }

            &.light .number {
              color: var(--text-secondary);
            }
          }

          .weight-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: center;
          }
        }
      }
    }

    .right-panel {
      .plate-section, .control-section, .recent-records {
        background: var(--bg-card);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-large);
        border: 2px solid var(--border-base);
      }

      .plate-section {
        .plate-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-lg);

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        .plate-display {
          text-align: center;
          margin-bottom: var(--spacing-lg);

          .plate-number {
            display: inline-flex;
            background: linear-gradient(135deg, #1e3a5f, #2d4a6b);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-base);
            font-size: 24px;
            font-weight: bold;
            margin-bottom: var(--spacing-md);
            border: 2px solid var(--border-base);

            &.valid {
              border-color: var(--el-color-success);
              box-shadow: 0 0 10px rgba(103, 194, 58, 0.3);
            }

            &.invalid {
              border-color: var(--el-color-danger);
              box-shadow: 0 0 10px rgba(245, 108, 108, 0.3);
            }

            .province {
              background: #1a5490;
              margin-right: 4px;
              padding: 0 4px;
              border-radius: 2px;
            }
          }

          .plate-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
            font-size: 14px;

            .el-icon {
              &.valid {
                color: var(--el-color-success);
              }

              &.invalid {
                color: var(--el-color-danger);
              }
            }
          }
        }

        .virtual-keyboard {
          .province-select {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
          }

          .keyboard-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: var(--spacing-xs);

            .el-button {
              aspect-ratio: 1;
              padding: 0;
              min-height: 32px;
            }
          }
        }
      }

      .control-section {
        .vehicle-status {
          margin-bottom: var(--spacing-lg);

          h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
          }

          .el-radio-group {
            width: 100%;

            :deep(.el-radio-button) {
              flex: 1;

              .el-radio-button__inner {
                width: 100%;
              }
            }
          }
        }

        .main-actions {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);

          .el-button {
            height: 48px;
            font-size: 16px;
            font-weight: 600;
          }
        }
      }

      .recent-records {
        flex: 1;
        display: flex;
        flex-direction: column;

        .records-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-md);

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        .records-list {
          flex: 1;
          overflow-y: auto;

          .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid var(--border-lighter);
            font-size: 12px;

            &:last-child {
              border-bottom: none;
            }

            .record-weight {
              color: var(--text-primary);
              font-weight: 500;
            }

            .record-time {
              color: var(--text-secondary);
              font-family: 'Courier New', monospace;
            }
          }
        }
      }
    }
  }

  .bottom-toolbar {
    height: 50px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-base);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);

    .toolbar-left {
      .system-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        .status-details {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
    }

    .toolbar-right {
      display: flex;
      gap: var(--spacing-sm);

      .el-button {
        .el-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}
</style>