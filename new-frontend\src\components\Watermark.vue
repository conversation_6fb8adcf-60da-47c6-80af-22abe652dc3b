<template>
  <div 
    class="watermark-container" 
    :class="positionClass"
    v-if="visible"
  >
    <div 
      class="watermark-content"
      :style="{
        fontSize: fontSize + 'px',
        opacity: opacity,
        color: color,
        transform: `rotate(${rotation}deg)`,
        fontWeight: fontWeight
      }"
    >
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
// 水印组件
// 作者：仕伟
// 用于在系统界面添加项目水印信息

import { computed } from 'vue'

interface WatermarkProps {
  /** 水印文本内容 */
  text?: string
  /** 是否显示水印 */
  visible?: boolean
  /** 字体大小 */
  fontSize?: number
  /** 透明度 0-1 */
  opacity?: number
  /** 文字颜色 */
  color?: string
  /** 旋转角度 */
  rotation?: number
  /** 字体粗细 */
  fontWeight?: string | number
  /** 水印位置 */
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center'
}

const props = withDefaults(defineProps<WatermarkProps>(), {
  text: '兴隆县清水河主要支流水生态环境治理工程',
  visible: true,
  fontSize: 16,
  opacity: 0.15,
  color: '#999999',
  rotation: -15,
  fontWeight: 400,
  position: 'bottom-right'
})

// 计算水印位置样式
const positionClass = computed(() => {
  return `watermark-${props.position}`
})
</script>

<style scoped lang="scss">
.watermark-container {
  position: fixed;
  pointer-events: none;
  user-select: none;
  z-index: 9999;
  
  // 不同位置的水印样式
  &.watermark-bottom-right {
    bottom: 20px;
    right: 20px;
  }
  
  &.watermark-bottom-left {
    bottom: 20px;
    left: 20px;
  }
  
  &.watermark-top-right {
    top: 20px;
    right: 20px;
  }
  
  &.watermark-top-left {
    top: 20px;
    left: 20px;
  }
  
  &.watermark-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .watermark-content {
    white-space: nowrap;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 2px;
    transition: all 0.3s ease;
    
    // 防止水印被选中或复制
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    // 防止水印被右键
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    
    // 增加文字阴影提升可读性
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .watermark-container {
    .watermark-content {
      font-size: 14px !important;
    }
  }
}

// 深色主题适配
[data-theme="dark"] .watermark-container {
  .watermark-content {
    color: rgba(255, 255, 255, 0.2) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// 浅色主题适配
[data-theme="light"] .watermark-container {
  .watermark-content {
    color: rgba(128, 128, 128, 0.4) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
  }
}

// 默认主题适配（没有data-theme属性时）- 适合白色背景的灰色
.watermark-container {
  .watermark-content {
    color: rgba(128, 128, 128, 0.3) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
  }
}
</style>