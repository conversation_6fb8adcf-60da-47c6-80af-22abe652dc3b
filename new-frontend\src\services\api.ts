import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API服务层 - 预留后端接口对接位置
// 作者：仕伟

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // TODO: 添加认证 token
    // const token = getToken()
    // if (token) {
    //   config.headers!.Authorization = `Bearer ${token}`
    // }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 统一处理响应格式
    if (data.code === 0) {
      return data.data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const status = error.response.status
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // TODO: 跳转到登录页
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(`请求失败: ${status}`)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 车辆相关接口
export const vehicleApi = {
  // 获取项目配置信息 - 真实接口调用
  getProjectConfig: (): Promise<ProjectConfig> => {
    return api.get('/car/getProjectId')
  },

  // 提交车辆数据 - 真实接口调用
  submitVehicleData: (data: VehicleSubmitData): Promise<VehicleSubmitResponse> => {
    console.log('提交车辆数据:', data)
    return api.post('/car/submitData', data)
  },

  // 查询车辆历史记录 - 真实接口调用
  queryVehicleHistory: (params: VehicleQueryParams): Promise<VehicleHistoryResponse> => {
    return api.get('/car/getDataByCarNumber', { params })
  },

  // 验证车牌号权限 - 真实接口调用
  validatePlateNumber: (plateNumber: string): Promise<PlateValidationResult> => {
    return api.get(`/car/getCarNumById?plateNumber=${encodeURIComponent(plateNumber)}`)
  },

  // 获取车辆统计信息 - 新增接口
  getVehicleStatistics: (params: TimeRangeParams): Promise<VehicleStatisticsResponse> => {
    return api.get('/car/statistics', { params })
  },

  // 获取实时车辆状态 - 新增接口
  getCurrentVehicleStatus: (): Promise<CurrentVehicleStatusResponse> => {
    return api.get('/car/currentStatus')
  }
}

// 称重相关接口
export const weightApi = {
  // 获取当前重量 - 真实接口调用
  getCurrentWeight: (): Promise<WeightData> => {
    return api.get('/weight/getCurrentWeight')
  },

  // 重量调零 - 真实接口调用
  tareWeight: (): Promise<TareWeightResponse> => {
    console.log('执行重量调零操作')
    return api.post('/weight/tare', {})
  },

  // 获取重量历史数据 - 真实接口调用
  getWeightHistory: (params: TimeRangeParams): Promise<WeightHistoryRecord[]> => {
    return api.get('/weight/history', { params })
  },

  // 获取重量统计信息 - 新增接口
  getWeightStatistics: (params: TimeRangeParams): Promise<WeightStatisticsResponse> => {
    return api.get('/weight/statistics', { params })
  }
}

// 设备控制接口
export const deviceApi = {
  // 获取设备状态 - 真实接口调用
  getDeviceStatus: (): Promise<DeviceStatusResponse> => {
    return api.get('/device/status')
  },

  // 触发抬杆 - 真实接口调用
  triggerGate: (): Promise<GateControlResponse> => {
    console.log('触发道闸抬杆')
    return api.post('/device/gate/trigger', {})
  },

  // 手动抓拍 - 真实接口调用
  capturePhoto: (cameraId?: number): Promise<CaptureResult> => {
    const data = cameraId ? { cameraId } : {}
    return api.post('/device/camera/capture', data)
  },

  // 设备重启 - 新增接口
  restartDevice: (deviceType: 'camera' | 'scale' | 'gate'): Promise<DeviceControlResponse> => {
    return api.post('/device/restart', { deviceType })
  },

  // 获取设备详细信息 - 新增接口
  getDeviceInfo: (deviceId: string): Promise<DeviceInfoResponse> => {
    return api.get(`/device/info/${deviceId}`)
  },

  // 获取摄像头配置信息 - 从后端获取摄像头列表
  getCameraConfig: (): Promise<CameraConfigResponse> => {
    // 调用真实的后端接口获取摄像头配置
    return api.get('/car/getProjectId')
  }
}

// 系统配置接口
export const configApi = {
  // 获取系统配置
  getSystemConfig: (): Promise<SystemConfig> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      resolve({
        mqttConfig: {
          host: '127.0.0.1',
          port: 8083,
          username: 'admin',
          password: 'admin123'
        },
        videoConfig: {
          primaryStream: 'rtmp://127.0.0.1/live/camera1',
          secondaryStream: 'rtmp://127.0.0.1/live/camera2'
        },
        weightConfig: {
          serialPort: 'COM3',
          baudRate: 9600,
          threshold: 30
        }
      })
    })
    // return api.get('/config')
  },

  // 更新系统配置
  updateSystemConfig: (config: SystemConfig): Promise<any> => {
    // TODO: 实际对接时替换为真实接口
    console.log('更新系统配置:', config)
    return new Promise((resolve) => {
      setTimeout(() => resolve({ success: true }), 500)
    })
    // return api.put('/config', config)
  }
}

// 导出默认实例
export default api

// TypeScript 类型定义
export interface ProjectConfig {
  projectId: string
  projectName: string
  stationName: string
  equipmentId: string
  version: string
}

export interface VehicleSubmitData {
  plateNumber: string
  weight: number
  status: 'IN' | 'OUT'
  timestamp: number
  images?: string[]
}

export interface VehicleQueryParams {
  plateNumber?: string
  startTime?: number
  endTime?: number
  status?: 'IN' | 'OUT'
  page?: number
  limit?: number
}

export interface VehicleRecord {
  id: string
  plateNumber: string
  weight: number
  timestamp: number
  status: 'IN' | 'OUT'
  images: string[]
}

export interface PlateValidationResult {
  valid: boolean
  plateNumber: string
  hasPermit: boolean
  permitInfo?: {
    permitId: string
    validUntil: number
    allowedWeight: number
  }
}

export interface WeightData {
  value: number
  unit: string
  stable: boolean
  timestamp: number
}

export interface TimeRangeParams {
  startTime: number
  endTime: number
}

export interface WeightHistoryRecord {
  timestamp: number
  weight: number
  stable: boolean
  plateNumber?: string
  status?: 'IN' | 'OUT'
}

export interface TareWeightResponse {
  code: number
  message: string
  success: boolean
  tareValue?: number
  timestamp?: number
}

export interface WeightStatisticsResponse {
  code: number
  data: {
    totalWeight: number
    averageWeight: number
    maxWeight: number
    minWeight: number
    recordCount: number
    timeRange: {
      startTime: number
      endTime: number
    }
  }
}

export interface DeviceStatusResponse {
  camera: 'online' | 'offline' | 'warning'
  scale: 'online' | 'offline' | 'warning'
  gate: 'online' | 'offline' | 'warning'
}

export interface CaptureResult {
  success: boolean
  imageUrl: string
  timestamp: number
  cameraId?: number
  fileSize?: number
}

export interface GateControlResponse {
  code: number
  success: boolean
  message: string
  gateStatus?: 'open' | 'closed' | 'opening' | 'closing'
  timestamp?: number
}

export interface DeviceControlResponse {
  code: number
  success: boolean
  message: string
  deviceType: string
  status?: string
  timestamp?: number
}

export interface DeviceInfoResponse {
  code: number
  data: {
    deviceId: string
    deviceName: string
    deviceType: 'camera' | 'scale' | 'gate'
    status: 'online' | 'offline' | 'warning'
    ip: string
    port?: number
    version?: string
    lastHeartbeat?: number
    uptime?: number
  }
}

export interface VehicleSubmitResponse {
  code: number
  success: boolean
  message: string
  data?: {
    id: string
    submitTime: number
  }
}

export interface VehicleHistoryResponse {
  code: number
  data: {
    records: VehicleRecord[]
    total: number
    page: number
    pageSize: number
  }
}

export interface VehicleStatisticsResponse {
  code: number
  data: {
    totalCount: number
    inCount: number
    outCount: number
    totalWeight: number
    averageWeight: number
    todayCount: number
    timeRange: {
      startTime: number
      endTime: number
    }
  }
}

export interface CurrentVehicleStatusResponse {
  code: number
  data: {
    currentVehicle?: {
      plateNumber: string
      status: 'IN' | 'OUT' | 'WEIGHING'
      weight?: number
      timestamp: number
      images?: string[]
    }
    queueCount: number
    lastProcessTime?: number
  }
}

export interface SystemConfig {
  mqttConfig: {
    host: string
    port: number
    username: string
    password: string
  }
  videoConfig: {
    primaryStream: string
    secondaryStream: string
  }
  weightConfig: {
    serialPort: string
    baudRate: number
    threshold: number
  }
}

// 摄像头配置相关接口类型定义
export interface CameraConfigItem {
  id: number
  ip: string
  port?: number
  channel?: number
  name: string
  sn?: string
  enabled?: boolean
  description?: string
}

export interface CameraConfigResponse {
  code: number
  message?: string
  data: {
    cameras: CameraConfigItem[]
    projectId?: string
    projectName?: string
  }
}