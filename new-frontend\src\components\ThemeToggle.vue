<template>
  <div class="theme-toggle">
    <el-tooltip :content="tooltipText" placement="bottom">
      <el-button
        :icon="themeIcon"
        circle
        @click="toggleTheme"
        class="theme-btn"
      />
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
// 主题切换组件
// 作者：仕伟

import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

// 当前主题图标
const themeIcon = computed(() => {
  return themeStore.isDark ? 'Sunny' : 'Moon'
})

// 提示文本
const tooltipText = computed(() => {
  return themeStore.isDark ? '切换到亮色模式' : '切换到暗色模式'
})

// 切换主题
const toggleTheme = () => {
  themeStore.toggleTheme()
  
  // 添加一个简单的反馈
  ElMessage({
    message: `已切换到${themeStore.isDark ? '暗色' : '亮色'}模式`,
    type: 'success',
    duration: 1000
  })
}
</script>

<style scoped lang="scss">
.theme-toggle {
  .theme-btn {
    transition: var(--transition-base);
    background: var(--bg-card);
    border: 1px solid var(--border-base);
    color: var(--text-regular);

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      transform: rotate(15deg);
    }

    &:active {
      transform: scale(0.95) rotate(15deg);
    }
  }
}
</style>