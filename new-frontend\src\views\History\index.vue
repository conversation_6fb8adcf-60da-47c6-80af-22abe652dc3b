<template>
  <div class="history-page">
    <div class="page-header">
      <h2>历史记录</h2>
      <p class="page-desc">查询和管理车辆进出记录</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="车牌号">
          <el-input 
            v-model="searchForm.plateNumber" 
            placeholder="请输入车牌号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="入场" value="IN" />
            <el-option label="出场" value="OUT" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'timestamp', order: 'descending' }"
      >
        <el-table-column prop="plateNumber" label="车牌号" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="primary" effect="dark">{{ row.plateNumber }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="重量" width="120" align="center">
          <template #default="{ row }">
            <div class="weight-cell">
              <span :class="getWeightClass(row.weight)" class="weight-value">
                {{ formatWeight(row.weight) }}
              </span>
              <span class="weight-unit">吨</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90" align="center">
          <template #default="{ row }">
            <div class="status-cell">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                size="small"
                effect="dark"
                class="status-tag"
              >
                <el-icon class="status-icon">
                  <ArrowRight v-if="row.status === 'IN'" />
                  <ArrowLeft v-else />
                </el-icon>
                {{ row.status === 'IN' ? '入场' : '出场' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="时间" width="200" align="center">
          <template #default="{ row }">
            <div class="time-display">
              <div class="time-main">
                <el-icon class="time-icon"><Clock /></el-icon>
                {{ formatTime(row.timestamp) }}
              </div>
              <div class="time-relative">{{ getRelativeTime(row.timestamp) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="照片" width="110" align="center">
          <template #default="{ row }">
            <el-button 
              v-if="row.images && row.images.length > 0"
              size="small" 
              type="primary" 
              link 
              @click="viewImages(row)"
              class="images-btn"
            >
              <el-icon><Picture /></el-icon>
              查看({{ row.images.length }})
            </el-button>
            <span v-else class="no-images">无照片</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="viewDetail(row)">
              详情
            </el-button>
            <el-button size="small" type="danger" link @click="deleteRecord(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog 
      v-model="imageDialogVisible" 
      title="车辆照片" 
      width="60%"
      align-center
    >
      <div class="image-preview">
        <el-image
          v-for="(image, index) in currentImages"
          :key="index"
          :src="image"
          :preview-src-list="currentImages"
          :initial-index="index"
          fit="contain"
          style="width: 200px; height: 150px; margin: 10px;"
          preview-teleported
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 历史记录页面
// 作者：仕伟

import { vehicleApi, type VehicleRecord } from '@/services/api'
import { ArrowRight, ArrowLeft, Clock, Picture, Search, Refresh, Download } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

// 启用相对时间插件
dayjs.extend(relativeTime)

// 搜索表单
const searchForm = reactive({
  plateNumber: '',
  dateRange: [] as string[],
  status: ''
})

// 表格数据
const tableData = ref<VehicleRecord[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 图片预览
const imageDialogVisible = ref(false)
const currentImages = ref<string[]>([])

// 搜索数据
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      plateNumber: searchForm.plateNumber,
      startTime: searchForm.dateRange[0] ? dayjs(searchForm.dateRange[0]).valueOf() : undefined,
      endTime: searchForm.dateRange[1] ? dayjs(searchForm.dateRange[1]).valueOf() : undefined,
      status: searchForm.status as 'IN' | 'OUT' | undefined,
      page: pagination.current,
      limit: pagination.pageSize
    }
    
    const result = await vehicleApi.queryVehicleHistory(params)
    tableData.value = result
    // pagination.total = result.total // TODO: 实际对接时从接口返回总数
    pagination.total = 100 // 模拟数据
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  searchForm.plateNumber = ''
  searchForm.dateRange = []
  searchForm.status = ''
  pagination.current = 1
  handleSearch()
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
  // TODO: 实现数据导出
}

// 查看图片
const viewImages = (row: VehicleRecord) => {
  currentImages.value = row.images
  imageDialogVisible.value = true
}

// 查看详情
const viewDetail = (row: VehicleRecord) => {
  ElMessage.info(`查看 ${row.plateNumber} 详情`)
  // TODO: 实现详情查看
}

// 删除记录
const deleteRecord = (row: VehicleRecord) => {
  ElMessageBox.confirm(`确定要删除车牌号为 ${row.plateNumber} 的记录吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    handleSearch()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  handleSearch()
}

// 获取重量样式类
const getWeightClass = (weight: number) => {
  if (weight > 40) return 'weight-overload'
  if (weight > 30) return 'weight-warning'
  if (weight < 5) return 'weight-light'
  return 'weight-normal'
}

// 格式化重量显示
const formatWeight = (weight: number) => {
  return weight.toFixed(1)
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  return status === 'IN' ? 'success' : 'warning'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format('MM-DD HH:mm:ss')
}

// 获取相对时间
const getRelativeTime = (timestamp: number) => {
  return dayjs(timestamp).fromNow()
}

// 页面加载时获取数据
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.history-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  .page-header {
    h2 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
    }

    .page-desc {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .search-card {
    flex-shrink: 0;
  }

  .table-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .el-table {
      flex: 1;
    }

    .pagination-wrapper {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: center;
    }
  }

  /* 重量单元格样式 */
  .weight-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    
    .weight-value {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      font-size: 14px;
      
      &.weight-normal {
        color: var(--text-primary);
      }
      
      &.weight-warning {
        color: var(--el-color-warning);
      }
      
      &.weight-overload {
        color: var(--color-weight-overload);
        font-weight: 700;
      }
      
      &.weight-light {
        color: var(--text-secondary);
      }
    }
    
    .weight-unit {
      font-size: 10px;
      color: var(--text-placeholder);
    }
  }
  
  /* 状态单元格样式 */
  .status-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .status-tag {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      
      .status-icon {
        font-size: 12px;
      }
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
      }
    }
  }
  
  /* 时间单元格样式 */
  .time-display {
    .time-main {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-size: 12px;
      color: var(--text-primary);
      font-family: 'Courier New', monospace;
      
      .time-icon {
        font-size: 12px;
        color: var(--el-color-primary);
      }
    }

    .time-relative {
      font-size: 10px;
      color: var(--text-placeholder);
      margin-top: 2px;
      text-align: center;
    }
  }
  
  /* 照片按钮样式 */
  .images-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .no-images {
    color: var(--text-placeholder);
    font-size: 12px;
  }

  .image-preview {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
    
    .el-image {
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>