<template>
  <el-dialog
    v-model="visible"
    title="历史记录"
    width="1400px"
    :modal="true"
    :close-on-click-modal="false"
  >
    <div class="history-content">
      <!-- 搜索筛选区域 -->
      <div class="search-section">
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="车牌号">
            <el-input
              v-model="searchForm.plateNumber"
              placeholder="输入车牌号"
              clearable
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item label="车辆状态">
            <el-select v-model="searchForm.vehicleStatus" placeholder="选择车辆状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="入场" value="IN" />
              <el-option label="出场" value="OUT" />
            </el-select>
          </el-form-item>
          <el-form-item label="称重状态">
            <el-select v-model="searchForm.status" placeholder="选择称重状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="超重" value="overweight" />
              <el-option label="异常" value="error" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据统计 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ stats.totalCount }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ stats.todayCount }}</div>
              <div class="stat-label">今日记录</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ stats.totalWeight }}</div>
              <div class="stat-label">总重量(吨)</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ stats.avgWeight }}</div>
              <div class="stat-label">平均重量(吨)</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 历史记录表格 -->
      <div class="table-section">
        <el-table
          :data="historyData"
          v-loading="loading"
          height="450"
          stripe
          border
          :cell-style="{ padding: '12px 8px' }"
          :header-cell-style="{ background: '#f8f9fa', color: '#606266', padding: '12px 8px' }"
        >
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="plateNumber" label="车牌号" width="150" align="center">
            <template #default="{ row }">
              <el-tag type="primary" size="small">{{ row.plateNumber }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="重量" width="120" align="center">
            <template #default="{ row }">
              <div class="weight-cell">
                <span :class="getWeightClass(row.weight)" class="weight-value">
                  {{ formatWeight(row.weight) }}
                </span>
                <span class="weight-unit">吨</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="vehicleStatus" label="车辆状态" width="110" align="center">
            <template #default="{ row }">
              <div class="status-cell">
                <el-tag
                  :type="getStatusTagType(row.vehicleStatus)"
                  size="small"
                  effect="dark"
                  class="status-tag"
                >
                  <el-icon class="status-icon">
                    <ArrowRight v-if="row.vehicleStatus === 'IN'" />
                    <ArrowLeft v-else />
                  </el-icon>
                  {{ row.vehicleStatus === 'IN' ? '入场' : '出场' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="称重状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作员" width="120" align="center" />
          <el-table-column prop="weighTime" label="提交时间" width="180" align="center">
            <template #default="{ row }">
              <div class="time-display">
                <div class="time-main">
                  <el-icon class="time-icon"><Clock /></el-icon>
                  {{ formatDateTime(row.weighTime) }}
                </div>
                <div class="time-relative">{{ getRelativeTime(row.weighTime) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="images" label="图片" width="120" align="center">
            <template #default="{ row }">
              <el-button
                v-if="row.images && row.images.length > 0"
                type="primary"
                size="small"
                link
                @click="viewImages(row)"
              >
                <el-icon><Picture /></el-icon>
                查看({{ row.images.length }})
              </el-button>
              <span v-else class="no-image">无图片</span>
            </template>
          </el-table-column>
          <!-- 操作列已隐藏 -->
          <!-- <el-table-column label="操作" width="160" fixed="right" align="center">
            <template #default="{ row }">
              <el-button type="primary" size="small" link @click="viewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="success" size="small" link @click="printRecord(row)">
                <el-icon><Printer /></el-icon>
                打印
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imageDialog.visible" title="过磅图片" width="80%">
      <div class="image-preview">
        <el-carousel :height="'400px'" indicator-position="outside">
          <el-carousel-item v-for="(image, index) in imageDialog.images" :key="index">
            <img :src="image" alt="过磅图片" class="preview-image" />
          </el-carousel-item>
        </el-carousel>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, Clock, Picture, View, Printer, ArrowRight, ArrowLeft } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

// 启用相对时间插件
dayjs.extend(relativeTime)

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  dateRange: [],
  plateNumber: '',
  vehicleStatus: '', // 车辆状态：入场/出场
  status: '' // 称重状态：正常/超重/异常
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  totalCount: 0,
  todayCount: 0,
  totalWeight: 0,
  avgWeight: 0
})

// 历史数据
const historyData = ref([])

// 图片预览对话框
const imageDialog = reactive({
  visible: false,
  images: []
})

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadHistoryData()
    loadStats()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

onMounted(() => {
  if (visible.value) {
    loadHistoryData()
    loadStats()
  }
})

// 模拟历史数据
const mockHistoryData = Array.from({ length: 50 }, (_, index) => ({
  id: index + 1,
  plateNumber: `京A${String(Math.random() * 10000).padStart(4, '0')}`,
  weight: Math.random() * 50000 + 10000,
  maxWeight: 40000,
  vehicleStatus: Math.random() > 0.5 ? 'IN' : 'OUT', // 入场或出场
  status: ['normal', 'overweight', 'error'][Math.floor(Math.random() * 3)],
  operator: '操作员' + (index % 3 + 1),
  weighTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  images: Math.random() > 0.5 ? [
    '/api/images/weigh1.jpg',
    '/api/images/weigh2.jpg'
  ] : []
}))

const loadHistoryData = async () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    historyData.value = mockHistoryData.slice(start, end)
    pagination.total = mockHistoryData.length
    loading.value = false
  }, 500)
}

const loadStats = () => {
  stats.totalCount = mockHistoryData.length
  stats.todayCount = Math.floor(Math.random() * 20) + 5
  stats.totalWeight = (mockHistoryData.reduce((sum, item) => sum + item.weight, 0) / 1000).toFixed(1)
  stats.avgWeight = (parseFloat(stats.totalWeight) / mockHistoryData.length).toFixed(1)
}

const handleSearch = () => {
  pagination.page = 1
  loadHistoryData()
}

const handleReset = () => {
  searchForm.dateRange = []
  searchForm.plateNumber = ''
  searchForm.vehicleStatus = ''
  searchForm.status = ''
  handleSearch()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadHistoryData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadHistoryData()
}

const getStatusType = (status: string) => {
  const typeMap = {
    normal: 'success',
    overweight: 'warning',
    error: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    normal: '正常',
    overweight: '超重',
    error: '异常'
  }
  return textMap[status] || '未知'
}

// 获取重量样式类
const getWeightClass = (weight: number) => {
  if (weight > 40000) return 'weight-overload' // 按kg计算
  if (weight > 30000) return 'weight-warning'
  if (weight < 5000) return 'weight-light'
  return 'weight-normal'
}

// 格式化重量显示
const formatWeight = (weight: number) => {
  return (weight / 1000).toFixed(1) // 转换为吨
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  return status === 'IN' ? 'success' : 'warning'
}

const formatDateTime = (date: Date) => {
  return dayjs(date).format('MM-DD HH:mm:ss')
}

// 获取相对时间
const getRelativeTime = (date: Date) => {
  return dayjs(date).fromNow()
}

const viewImages = (row: any) => {
  imageDialog.images = row.images
  imageDialog.visible = true
}

const viewDetail = (row: any) => {
  ElMessage.info('详情功能开发中...')
}

const printRecord = (row: any) => {
  ElMessage.info('打印功能开发中...')
}
</script>

<style scoped>
.history-content {
  min-height: 600px;
}

.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  padding: 20px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.table-section {
  background: #fff;
  border-radius: 4px;
}

.pagination-section {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
}

/* 重量单元格样式 */
.weight-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  
  .weight-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 14px;
    
    &.weight-normal {
      color: var(--text-primary);
    }
    
    &.weight-warning {
      color: var(--el-color-warning);
    }
    
    &.weight-overload {
      color: #f56c6c;
      font-weight: 700;
    }
    
    &.weight-light {
      color: var(--text-secondary);
    }
  }
  
  .weight-unit {
    font-size: 10px;
    color: var(--text-placeholder);
  }
}

/* 状态单元格样式 */
.status-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .status-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    .status-icon {
      font-size: 12px;
    }
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
    }
  }
}

/* 时间单元格样式 */
.time-display {
  .time-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    
    .time-icon {
      font-size: 12px;
      color: var(--el-color-primary);
    }
  }

  .time-relative {
    font-size: 10px;
    color: var(--text-placeholder);
    margin-top: 2px;
    text-align: center;
  }
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

.image-preview {
  text-align: center;
}

.preview-image {
  width: 100%;
  height: 400px;
  object-fit: contain;
}

.dialog-footer {
  text-align: right;
}

/* 夜间模式支持 */
:global(.dark) .search-section {
  background: #2d2d30;
}

:global(.dark) .stat-card {
  background: #2d2d30;
  border-color: #4d4d50;
  color: #ffffff;
}

:global(.dark) .table-section {
  background: #2d2d30;
}
</style>