<template>
  <div class="fullscreen-watermark" v-if="visible">
    <div 
      v-for="(row, rowIndex) in watermarkGrid" 
      :key="`row-${rowIndex}`"
      class="watermark-row"
    >
      <div
        v-for="(col, colIndex) in row"
        :key="`item-${rowIndex}-${colIndex}`"
        class="watermark-item"
        :style="{
          fontSize: fontSize + 'px',
          opacity: opacity,
          color: color,
          transform: `rotate(${rotation}deg)`,
          fontWeight: fontWeight
        }"
      >
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 全屏水印组件
// 作者：仕伟
// 创建铺满整个屏幕的水印效果

import { computed, onMounted, onUnmounted, ref } from 'vue'

interface FullScreenWatermarkProps {
  /** 水印文本内容 */
  text?: string
  /** 是否显示水印 */
  visible?: boolean
  /** 字体大小 */
  fontSize?: number
  /** 透明度 0-1 */
  opacity?: number
  /** 文字颜色 */
  color?: string
  /** 旋转角度 */
  rotation?: number
  /** 字体粗细 */
  fontWeight?: string | number
  /** 水印间距 */
  spacing?: number
}

const props = withDefaults(defineProps<FullScreenWatermarkProps>(), {
  text: '兴隆县清水河主要支流水生态环境治理工程',
  visible: true,
  fontSize: 16,
  opacity: 0.08,
  color: '#999999',
  rotation: -15,
  fontWeight: 400,
  spacing: 200
})

// 屏幕尺寸
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)

// 计算水印网格
const watermarkGrid = computed(() => {
  const verticalSpacing = 160  // 垂直间距
  const horizontalSpacing = 200 // 水平间距
  const rows = Math.ceil(screenHeight.value / verticalSpacing) + 2
  const itemsPerRow = Math.ceil(screenWidth.value / horizontalSpacing) + 2
  
  const grid = []
  for (let row = 0; row < rows; row++) {
    const rowData = []
    for (let col = 0; col < itemsPerRow; col++) {
      rowData.push({ 
        id: `${row}-${col}`,
        row, 
        col 
      })
    }
    grid.push(rowData)
  }
  return grid
})

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.fullscreen-watermark {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  user-select: none;
  z-index: 1000;
  overflow: hidden;
  
  .watermark-row {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 160px;
    width: 100%;
    
    .watermark-item {
      white-space: nowrap;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      letter-spacing: 2px;
      
      // 防止水印被选中或复制
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      
      // 防止水印被右键
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      
      // 增加文字阴影提升可读性
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
    }
  }
  
  // 交替行偏移，创建更自然的水印效果
  .watermark-row:nth-child(even) {
    transform: translateX(100px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fullscreen-watermark {
    .watermark-item {
      font-size: 12px !important;
    }
  }
}

// 深色主题适配
[data-theme="dark"] .fullscreen-watermark {
  .watermark-item {
    color: rgba(255, 255, 255, 0.08) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// 浅色主题适配
[data-theme="light"] .fullscreen-watermark {
  .watermark-item {
    color: rgba(128, 128, 128, 0.12) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.6);
  }
}

// 默认主题适配 - 适合白色背景的中性灰色
.fullscreen-watermark {
  .watermark-item {
    color: rgba(128, 128, 128, 0.1) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.4);
  }
}
</style>