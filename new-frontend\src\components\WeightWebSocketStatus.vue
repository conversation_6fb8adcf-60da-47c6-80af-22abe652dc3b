<template>
  <div class="websocket-status">
    <div class="status-indicator">
      <div class="status-dot" :class="statusClass"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>
    
    <div class="status-actions">
      <el-button 
        v-if="!isConnected" 
        size="small" 
        type="primary" 
        @click="handleConnect"
        :loading="connecting"
      >
        重新连接
      </el-button>
      
      <el-button 
        v-if="isConnected" 
        size="small" 
        @click="handleDisconnect"
      >
        断开连接
      </el-button>
    </div>
    
    <!-- 连接详情 -->
    <div v-if="showDetails" class="status-details">
      <div class="detail-item">
        <span class="label">连接状态：</span>
        <span class="value">{{ connectionStatus }}</span>
      </div>
      <div v-if="reconnectAttempts > 0" class="detail-item">
        <span class="label">重连次数：</span>
        <span class="value">{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>
      </div>
      <div class="detail-item">
        <span class="label">WebSocket地址：</span>
        <span class="value">ws://localhost:8077/ws/weight</span>
      </div>
    </div>
    
    <!-- 切换详情显示 -->
    <el-button 
      text 
      size="small" 
      @click="showDetails = !showDetails"
      style="margin-top: 8px;"
    >
      {{ showDetails ? '收起详情' : '查看详情' }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
// WebSocket连接状态组件
// 作者：仕伟

import { useWeightWebSocket } from '@/services/websocket'

const {
  isConnected,
  connectionStatus,
  reconnectAttempts,
  maxReconnectAttempts,
  connect,
  disconnect
} = useWeightWebSocket()

// 组件状态
const connecting = ref(false)
const showDetails = ref(false)

// 状态样式类
const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'status-connected'
    case 'connecting':
    case 'reconnecting':
      return 'status-connecting'
    case 'disconnected':
    default:
      return 'status-disconnected'
  }
})

// 状态文本
const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'reconnecting':
      return `重连中... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
    case 'disconnected':
    default:
      return '未连接'
  }
})

// 连接操作
const handleConnect = async () => {
  connecting.value = true
  try {
    await connect()
    ElMessage.success('WebSocket连接成功')
  } catch (error) {
    ElMessage.error('WebSocket连接失败')
  } finally {
    connecting.value = false
  }
}

// 断开操作
const handleDisconnect = () => {
  disconnect()
  ElMessage.info('已断开WebSocket连接')
}
</script>

<style scoped lang="scss">
.websocket-status {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-base);
  background: var(--bg-color-light);
  border: 1px solid var(--border-base);

  .status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      
      &.status-connected {
        background: var(--el-color-success);
        box-shadow: 0 0 4px var(--el-color-success);
      }
      
      &.status-connecting {
        background: var(--el-color-warning);
        animation: pulse 1.5s ease-in-out infinite;
      }
      
      &.status-disconnected {
        background: var(--el-color-danger);
      }
    }

    .status-text {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }

  .status-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
  }

  .status-details {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-color);
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border-lighter);

    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-xs);
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: var(--text-secondary);
      }

      .value {
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>