<template>
  <el-dialog
    v-model="visible"
    title="系统设置"
    width="600px"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="settings-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 设备配置 -->
        <el-tab-pane label="设备配置" name="device">
          <el-form :model="deviceSettings" label-width="120px">
            <el-form-item label="串口设置">
              <el-select v-model="deviceSettings.serialPort" placeholder="选择串口">
                <el-option label="COM1" value="COM1" />
                <el-option label="COM2" value="COM2" />
                <el-option label="COM3" value="COM3" />
              </el-select>
            </el-form-item>
            <el-form-item label="波特率">
              <el-select v-model="deviceSettings.baudRate">
                <el-option label="9600" :value="9600" />
                <el-option label="115200" :value="115200" />
              </el-select>
            </el-form-item>
            <el-form-item label="摄像头地址">
              <el-input v-model="deviceSettings.cameraUrl" placeholder="rtsp://..." />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 系统配置 -->
        <el-tab-pane label="系统配置" name="system">
          <el-form :model="systemSettings" label-width="120px">
            <el-form-item label="自动过磅">
              <el-switch v-model="systemSettings.autoWeighing" />
            </el-form-item>
            <el-form-item label="夜间模式">
              <el-switch v-model="systemSettings.nightMode" @change="toggleNightMode" />
            </el-form-item>
            <el-form-item label="声音提示">
              <el-switch v-model="systemSettings.soundAlert" />
            </el-form-item>
            <el-form-item label="打印设置">
              <el-switch v-model="systemSettings.autoPrint" />
            </el-form-item>
            
            <!-- 水印设置 -->
            <el-divider content-position="left">水印设置</el-divider>
            <el-form-item label="显示水印">
              <el-switch v-model="systemSettings.showWatermark" />
            </el-form-item>
            <el-form-item label="水印文本" v-if="systemSettings.showWatermark">
              <el-input 
                v-model="systemSettings.watermarkText" 
                placeholder="请输入水印文本"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="水印透明度" v-if="systemSettings.showWatermark">
              <el-slider 
                v-model="systemSettings.watermarkOpacity" 
                :min="0.05" 
                :max="0.5" 
                :step="0.05"
                :format-tooltip="(val: number) => `${Math.round(val * 100)}%`"
              />
            </el-form-item>
            <el-form-item label="水印模式" v-if="systemSettings.showWatermark">
              <el-radio-group v-model="systemSettings.watermarkMode">
                <el-radio value="fullscreen">全屏水印</el-radio>
                <el-radio value="corner">角落水印</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="水印位置" v-if="systemSettings.showWatermark && systemSettings.watermarkMode === 'corner'">
              <el-select v-model="systemSettings.watermarkPosition">
                <el-option label="右下角" value="bottom-right" />
                <el-option label="左下角" value="bottom-left" />
                <el-option label="右上角" value="top-right" />
                <el-option label="左上角" value="top-left" />
                <el-option label="居中" value="center" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 网络配置 -->
        <el-tab-pane label="网络配置" name="network">
          <el-form :model="networkSettings" label-width="120px">
            <el-form-item label="服务器地址">
              <el-input v-model="networkSettings.serverUrl" />
            </el-form-item>
            <el-form-item label="WebSocket端口">
              <el-input v-model="networkSettings.wsPort" type="number" />
            </el-form-item>
            <el-form-item label="连接状态">
              <el-tag :type="networkSettings.connected ? 'success' : 'danger'">
                {{ networkSettings.connected ? '已连接' : '未连接' }}
              </el-tag>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 关于系统 -->
        <el-tab-pane label="关于系统" name="about">
          <div class="about-content">
            <div class="system-info">
              <h3>采砂一体机系统</h3>
              <p>版本: v2.0.0</p>
              <p>构建时间: 2024-01-15</p>
              <p>硬件平台: Industrial PC</p>
            </div>
            <el-divider />
            <div class="hardware-status">
              <h4>硬件状态</h4>
              <div class="status-grid">
                <div class="status-item">
                  <span class="label">摄像头:</span>
                  <el-tag type="success" size="small">正常</el-tag>
                </div>
                <div class="status-item">
                  <span class="label">称重传感器:</span>
                  <el-tag type="success" size="small">正常</el-tag>
                </div>
                <div class="status-item">
                  <span class="label">闸机控制:</span>
                  <el-tag type="success" size="small">正常</el-tag>
                </div>
                <div class="status-item">
                  <span class="label">网络连接:</span>
                  <el-tag type="success" size="small">正常</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存设置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'settings-updated', settings: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)
const activeTab = ref('device')

// 设备设置
const deviceSettings = reactive({
  serialPort: 'COM1',
  baudRate: 9600,
  cameraUrl: 'rtsp://admin:admin@*************:554/stream'
})

// 系统设置
const systemSettings = reactive({
  autoWeighing: true,
  nightMode: false,
  soundAlert: true,
  autoPrint: false,
  // 水印设置
  showWatermark: true,
  watermarkText: '兴隆县清水河主要支流水生态环境治理工程',
  watermarkOpacity: 0.15,
  watermarkMode: 'fullscreen',
  watermarkPosition: 'bottom-right'
})

// 网络设置
const networkSettings = reactive({
  serverUrl: 'http://localhost:8080',
  wsPort: 8081,
  connected: true
})

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const toggleNightMode = (enabled: boolean) => {
  const html = document.documentElement
  if (enabled) {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

const handleSave = () => {
  const allSettings = {
    device: deviceSettings,
    system: systemSettings,
    network: networkSettings
  }
  
  emit('settings-updated', allSettings)
  ElMessage.success('设置已保存')
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.settings-content {
  min-height: 400px;
}

.about-content {
  text-align: center;
}

.system-info h3 {
  margin-bottom: 16px;
  color: #409eff;
  font-size: 20px;
}

.system-info p {
  margin: 8px 0;
  color: #666;
}

.hardware-status {
  margin-top: 24px;
}

.hardware-status h4 {
  margin-bottom: 16px;
  text-align: left;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  text-align: left;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.status-item .label {
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

/* 夜间模式支持 */
:global(.dark) .status-item {
  background: #2d2d30;
  color: #ffffff;
}

:global(.dark) .system-info p {
  color: #cccccc;
}
</style>