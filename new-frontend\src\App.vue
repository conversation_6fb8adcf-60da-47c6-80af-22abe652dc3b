<template>
  <div id="app" :data-theme="currentTheme">
    <router-view />
    
    <!-- 全局水印：复用 FullScreenWatermark 组件并接入 Pinia 配置 -->
    <FullScreenWatermark 
      v-if="watermarkStore.isVisible"
      :text="watermarkStore.watermarkText"
      :font-size="watermarkStore.fontSize"
      :opacity="watermarkStore.opacity"
      :rotation="watermarkStore.rotation"
    />
  </div>
</template>

<script setup lang="ts">
// 河北省采(弃)砂监管一体机系统 - 主应用组件
// 作者：仕伟

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useWatermarkStore } from '@/stores/watermark'
import { useForceFullscreen } from '@/composables/useForceFullscreen'
import { useWeightWebSocket } from '@/services/websocket'
import FullScreenWatermark from '@/components/FullScreenWatermark.vue'

const themeStore = useThemeStore()
const watermarkStore = useWatermarkStore()
const currentTheme = computed(() => themeStore.theme)

// 使用强制全屏功能
const { initForceFullscreen, cleanupForceFullscreen } = useForceFullscreen()

// 使用WebSocket连接
const { autoConnect, disconnect } = useWeightWebSocket()

onMounted(() => {
  console.log('🚀 河北省采砂监管一体机系统启动')
  
  // 加载水印配置
  watermarkStore.loadFromLocalStorage()
  
  // 初始化强制全屏功能
  initForceFullscreen()
  
  // 自动连接WebSocket
  setTimeout(() => {
    autoConnect().catch(error => {
      console.error('WebSocket自动连接失败:', error)
    })
  }, 1000) // 延迟1秒连接，确保应用完全加载
})

onUnmounted(() => {
  console.log('🛑 应用组件销毁，清理强制全屏功能和WebSocket连接')
  // 清理强制全屏功能
  cleanupForceFullscreen()
  // 断开WebSocket连接
  disconnect()
})
</script>

<style>
/* NProgress 样式自定义 */
#nprogress .bar {
  background: var(--el-color-primary) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
}

#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}
</style>
