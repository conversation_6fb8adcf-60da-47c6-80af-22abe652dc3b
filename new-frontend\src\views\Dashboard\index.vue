<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>采砂监管控制台</h2>
      <p class="page-desc">实时监控采砂车辆进出场情况</p>
    </div>

    <!-- 主控制面板 -->
    <div class="dashboard-grid">
      <!-- 左侧监控区 -->
      <div class="monitor-section">
        <VideoMonitor />
        <WeightDisplay />
      </div>

      <!-- 右侧操作区 -->
      <div class="control-section">
        <PlateInputCard />
        <ActionPanel />
      </div>
    </div>

    <!-- 底部数据展示 -->
    <div class="data-section">
      <RecentRecordsTable />
    </div>
  </div>
</template>

<script setup lang="ts">
// 采砂监管控制台 - 主仪表盘页面
// 作者：仕伟

import VideoMonitor from './components/VideoMonitor.vue'
import WeightDisplay from './components/WeightDisplay.vue'
import PlateInputCard from './components/PlateInputCard.vue'
import ActionPanel from './components/ActionPanel.vue'
import RecentRecordsTable from './components/RecentRecordsTable.vue'
import { useWebSocket } from '@/services/websocket'
import { useDeviceStore } from '@/stores/device'

const deviceStore = useDeviceStore()
const { connect, subscribe, isConnected } = useWebSocket()

// 页面初始化
onMounted(async () => {
  // 连接 WebSocket
  await connect()
  
  // 订阅设备状态更新
  subscribe('DEVICE_STATUS', (data) => {
    deviceStore.updateAllDeviceStatus(data)
  })

  // 初始化设备监控
  deviceStore.initDeviceMonitoring()
})
</script>

<style scoped lang="scss">
.dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  .page-header {
    margin-bottom: var(--spacing-md);

    h2 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
    }

    .page-desc {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .dashboard-grid {
    display: grid;
    gap: var(--spacing-lg);
    flex: 1;
    min-height: 0;
    
    /* 超大屏：三列布局 */
    @media (min-width: $breakpoint-xxl) {
      grid-template-columns: 1fr 420px 420px;
      gap: var(--spacing-xl);
    }
    
    /* 大屏：两列布局（默认） */
    @media (min-width: $breakpoint-xl) and (max-width: calc(#{$breakpoint-xxl} - 1px)) {
      grid-template-columns: 1fr 420px;
    }
    
    /* 中大屏：两列窄布局 */
    @media (min-width: $breakpoint-lg) and (max-width: calc(#{$breakpoint-xl} - 1px)) {
      grid-template-columns: 1fr 380px;
      gap: var(--spacing-md);
    }

    /* 中屏及以下：单列布局 */
    @media (max-width: calc(#{$breakpoint-lg} - 1px)) {
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
    }

    .monitor-section {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
      min-height: 0;
    }

    .control-section {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
      
      /* 超大屏时将控制区分为两列 */
      @media (min-width: $breakpoint-xxl) {
        grid-column: 2 / 4;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
      }

      @media (min-width: $breakpoint-lg) and (max-width: calc(#{$breakpoint-lg} - 1px)) {
        flex-direction: row;
        gap: var(--spacing-sm);
      }

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
        gap: var(--spacing-md);
      }
    }
  }

  .data-section {
    /* 超大屏：更大的记录表格 */
    @media (min-width: $breakpoint-xxl) {
      flex: 0 0 360px;
      min-height: 360px;
    }
    
    /* 大屏：正常大小 */
    @media (min-width: $breakpoint-xl) and (max-width: calc(#{$breakpoint-xxl} - 1px)) {
      flex: 0 0 320px;
      min-height: 320px;
    }
    
    /* 中大屏：稍小 */
    @media (min-width: $breakpoint-lg) and (max-width: calc(#{$breakpoint-xl} - 1px)) {
      flex: 0 0 300px;
      min-height: 300px;
    }
    
    /* 中小屏：紧凑 */
    @media (max-width: calc(#{$breakpoint-lg} - 1px)) {
      flex: 0 0 280px;
      min-height: 280px;
    }

    @media (max-width: $breakpoint-md) {
      flex: 0 0 240px;
      min-height: 240px;
    }
  }
}
</style>