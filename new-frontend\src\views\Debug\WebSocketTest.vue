<template>
  <div class="websocket-test">
    <el-card class="test-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Connection /></el-icon>
          <span class="header-title">WebSocket重量数据调试</span>
        </div>
      </template>

      <!-- WebSocket连接状态 -->
      <div class="connection-section">
        <h3>连接状态</h3>
        <WeightWebSocketStatus />
      </div>

      <!-- 消息日志 -->
      <div class="message-section">
        <div class="section-header">
          <h3>消息日志</h3>
          <div class="section-actions">
            <el-button size="small" @click="clearMessages">清空日志</el-button>
            <el-button size="small" type="primary" @click="sendTestPing">发送心跳</el-button>
          </div>
        </div>
        
        <div class="message-log" ref="messageLogRef">
          <div 
            v-for="(message, index) in messageLog" 
            :key="index"
            class="message-item"
            :class="`message-${message.type}`"
          >
            <div class="message-header">
              <span class="message-time">{{ message.time }}</span>
              <el-tag :type="getMessageTagType(message.type)" size="small">
                {{ message.type }}
              </el-tag>
            </div>
            <div class="message-content">
              <pre>{{ JSON.stringify(message.data, null, 2) }}</pre>
            </div>
          </div>
          
          <div v-if="messageLog.length === 0" class="empty-message">
            暂无消息，等待WebSocket数据推送...
          </div>
        </div>
      </div>

      <!-- 实时数据展示 -->
      <div class="data-section">
        <h3>实时重量数据</h3>
        <div class="data-grid">
          <div class="data-item">
            <div class="data-label">当前重量</div>
            <div class="data-value">{{ currentWeight.toFixed(3) }} kg</div>
          </div>
          <div class="data-item">
            <div class="data-label">数据状态</div>
            <div class="data-value">
              <el-tag :type="currentStatus === 'normal' ? 'success' : 'danger'">
                {{ currentStatus }}
              </el-tag>
            </div>
          </div>
          <div class="data-item">
            <div class="data-label">最后更新</div>
            <div class="data-value">{{ lastUpdateTime }}</div>
          </div>
          <div class="data-item">
            <div class="data-label">消息计数</div>
            <div class="data-value">{{ messageCount }}</div>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <h3>消息统计</h3>
        <div class="stats-grid">
          <!-- 基础消息统计 -->
          <div class="stat-card">
            <div class="stat-title">重量数据</div>
            <div class="stat-value">{{ weightMessageCount }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">状态消息</div>
            <div class="stat-value">{{ statusMessageCount }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">错误消息</div>
            <div class="stat-value">{{ errorMessageCount }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">心跳消息</div>
            <div class="stat-value">{{ heartbeatMessageCount }}</div>
          </div>
          
          <!-- RG设备消息统计 -->
          <div class="stat-card rg-card">
            <div class="stat-title">🚗 车辆识别</div>
            <div class="stat-value">{{ rgVehicleRecognitionCount }}</div>
          </div>
          <div class="stat-card rg-card">
            <div class="stat-title">📸 抓图结果</div>
            <div class="stat-value">{{ rgSnapshotResultCount }}</div>
          </div>
          <div class="stat-card rg-card">
            <div class="stat-title">📡 设备状态</div>
            <div class="stat-value">{{ rgDeviceStatusCount }}</div>
          </div>
          <div class="stat-card rg-card">
            <div class="stat-title">📊 交通统计</div>
            <div class="stat-value">{{ rgTrafficStatisticsCount }}</div>
          </div>
          <div class="stat-card rg-card">
            <div class="stat-title">⚡ 命令回执</div>
            <div class="stat-value">{{ rgCommandReplyCount }}</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// WebSocket测试调试组件
// 作者：仕伟

import { useWeightWebSocket, type WeightData } from '@/services/websocket'
import WeightWebSocketStatus from '@/components/WeightWebSocketStatus.vue'

// 消息日志结构
interface MessageLogItem {
  time: string
  type: string
  data: any
}

// 组件状态
const messageLog = ref<MessageLogItem[]>([])
const messageLogRef = ref<HTMLElement>()
const currentWeight = ref(0)
const currentStatus = ref('normal')
const lastUpdateTime = ref('未更新')
const messageCount = ref(0)
const weightMessageCount = ref(0)
const statusMessageCount = ref(0)
const errorMessageCount = ref(0)
const heartbeatMessageCount = ref(0)
// RG设备消息统计
const rgVehicleRecognitionCount = ref(0)
const rgSnapshotResultCount = ref(0)
const rgDeviceStatusCount = ref(0)
const rgTrafficStatisticsCount = ref(0)
const rgCommandReplyCount = ref(0)

// WebSocket连接
const {
  subscribeWeightData,
  subscribeConnectionStatus,
  subscribeErrorMessage,
  subscribeHeartbeat,
  subscribeRgVehicleRecognition,
  subscribeRgSnapshotResult,
  subscribeRgDeviceStatus,
  subscribeRgTrafficStatistics,
  subscribeRgCommandReply,
  sendHeartbeat
} = useWeightWebSocket()

// 添加消息到日志
const addMessage = (type: string, data: any) => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString('zh-CN')
  
  messageLog.value.push({
    time: timeStr,
    type,
    data
  })
  
  // 限制日志条数
  if (messageLog.value.length > 100) {
    messageLog.value.shift()
  }
  
  messageCount.value++
  
  // 自动滚动到底部
  nextTick(() => {
    if (messageLogRef.value) {
      messageLogRef.value.scrollTop = messageLogRef.value.scrollHeight
    }
  })
}

// 处理重量数据
const handleWeightData = (data: WeightData) => {
  weightMessageCount.value++
  addMessage('weight_data', data)
  
  if (data.hasData && data.weight !== null) {
    currentWeight.value = data.weight
    currentStatus.value = data.status || 'normal'
    lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
  }
}

// 处理连接状态
const handleConnectionStatus = (data: any) => {
  statusMessageCount.value++
  addMessage('connection_status', data)
}

// 处理错误消息
const handleErrorMessage = (data: any) => {
  errorMessageCount.value++
  addMessage('error_message', data)
}

// 处理心跳消息
const handleHeartbeat = (data: any) => {
  heartbeatMessageCount.value++
  addMessage('heartbeat', data)
}

// 处理RG车辆识别消息
const handleRgVehicleRecognition = (data: any) => {
  rgVehicleRecognitionCount.value++
  addMessage('rg_vehicle_recognition', data)
}

// 处理RG抓图结果
const handleRgSnapshotResult = (data: any) => {
  rgSnapshotResultCount.value++
  addMessage('rg_snapshot_result', data)
}

// 处理RG设备状态
const handleRgDeviceStatus = (data: any) => {
  rgDeviceStatusCount.value++
  addMessage('rg_device_status', data)
}

// 处理RG交通统计
const handleRgTrafficStatistics = (data: any) => {
  rgTrafficStatisticsCount.value++
  addMessage('rg_traffic_statistics', data)
}

// 处理RG命令回执
const handleRgCommandReply = (data: any) => {
  rgCommandReplyCount.value++
  addMessage('rg_command_reply', data)
}

// 获取消息标签类型
const getMessageTagType = (type: string) => {
  switch (type) {
    case 'weight_data':
    case 'WEIGHT_DATA':
      return 'primary'
    case 'connection_status':
      return 'success'
    case 'error_message':
      return 'danger'
    case 'heartbeat':
      return 'info'
    // RG设备消息类型
    case 'rg_vehicle_recognition':
      return 'success'
    case 'rg_snapshot_result':
      return 'warning'
    case 'rg_device_status':
      return 'info'
    case 'rg_traffic_statistics':
      return 'primary'
    case 'rg_command_reply':
      return 'success'
    // 兼容车牌识别
    case 'plate_recognition':
    case 'PLATE_RECOGNITION':
      return 'success'
    default:
      return ''
  }
}

// 清空消息日志
const clearMessages = () => {
  messageLog.value = []
  messageCount.value = 0
  weightMessageCount.value = 0
  statusMessageCount.value = 0
  errorMessageCount.value = 0
  heartbeatMessageCount.value = 0
  // 清空RG消息统计
  rgVehicleRecognitionCount.value = 0
  rgSnapshotResultCount.value = 0
  rgDeviceStatusCount.value = 0
  rgTrafficStatisticsCount.value = 0
  rgCommandReplyCount.value = 0
}

// 发送测试心跳
const sendTestPing = () => {
  sendHeartbeat()
  addMessage('sent_ping', { message: '发送心跳测试' })
}

// 组件挂载时订阅消息
onMounted(() => {
  // 订阅重量数据相关消息
  subscribeWeightData(handleWeightData)
  subscribeConnectionStatus(handleConnectionStatus)
  subscribeErrorMessage(handleErrorMessage)
  subscribeHeartbeat(handleHeartbeat)
  
  // 订阅RG设备相关消息
  subscribeRgVehicleRecognition(handleRgVehicleRecognition)
  subscribeRgSnapshotResult(handleRgSnapshotResult)
  subscribeRgDeviceStatus(handleRgDeviceStatus)
  subscribeRgTrafficStatistics(handleRgTrafficStatistics)
  subscribeRgCommandReply(handleRgCommandReply)
  
  console.log('WebSocket调试页面已启动，开始监听所有消息类型')
})
</script>

<style scoped lang="scss">
.websocket-test {
  padding: var(--spacing-lg);
  
  .test-card {
    max-width: 1200px;
    margin: 0 auto;
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .header-icon {
      color: var(--el-color-primary);
      font-size: 18px;
    }
    
    .header-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .connection-section,
  .message-section,
  .data-section,
  .stats-section {
    margin-bottom: var(--spacing-xl);
    
    h3 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 600;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .section-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .message-log {
    height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-base);
    border-radius: var(--border-radius-base);
    padding: var(--spacing-sm);
    background: var(--bg-color-light);
    
    .message-item {
      margin-bottom: var(--spacing-md);
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-small);
      
      &.message-weight_data,
      &.message-WEIGHT_DATA {
        background: rgba(64, 158, 255, 0.1);
        border-left: 3px solid var(--el-color-primary);
      }
      
      &.message-connection_status {
        background: rgba(103, 194, 58, 0.1);
        border-left: 3px solid var(--el-color-success);
      }
      
      &.message-error_message {
        background: rgba(245, 108, 108, 0.1);
        border-left: 3px solid var(--el-color-danger);
      }
      
      &.message-heartbeat {
        background: rgba(144, 147, 153, 0.1);
        border-left: 3px solid var(--el-color-info);
      }
    }
    
    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-xs);
      
      .message-time {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
    
    .message-content {
      pre {
        margin: 0;
        font-size: 12px;
        color: var(--text-primary);
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
    
    .empty-message {
      text-align: center;
      color: var(--text-secondary);
      margin-top: var(--spacing-xl);
    }
  }

  .data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    
    .data-item {
      padding: var(--spacing-md);
      border-radius: var(--border-radius-base);
      background: var(--bg-color-light);
      border: 1px solid var(--border-base);
      
      .data-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
      }
      
      .data-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    
    .stat-card {
      text-align: center;
      padding: var(--spacing-lg);
      border-radius: var(--border-radius-base);
      background: var(--bg-color-light);
      border: 1px solid var(--border-base);
      
      .stat-title {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--el-color-primary);
      }

      &.rg-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;

        .stat-title {
          color: rgba(255, 255, 255, 0.9);
        }

        .stat-value {
          color: white;
        }
      }
    }
  }
}
</style>