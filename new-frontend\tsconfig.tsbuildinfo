{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./src/stores/theme.ts", "./src/stores/watermark.ts", "./src/composables/useforcefullscreen.ts", "./src/services/websocket.ts", "./src/components/fullscreenwatermark.vue.ts", "./src/app.vue.ts", "./node_modules/element-plus/es/utils/vue3.3.polyfill.d.ts", "./node_modules/dayjs/locale/types.d.ts", "./node_modules/dayjs/locale/index.d.ts", "./node_modules/dayjs/index.d.ts", "./node_modules/@types/lodash/common/common.d.ts", "./node_modules/@types/lodash/common/array.d.ts", "./node_modules/@types/lodash/common/collection.d.ts", "./node_modules/@types/lodash/common/date.d.ts", "./node_modules/@types/lodash/common/function.d.ts", "./node_modules/@types/lodash/common/lang.d.ts", "./node_modules/@types/lodash/common/math.d.ts", "./node_modules/@types/lodash/common/number.d.ts", "./node_modules/@types/lodash/common/object.d.ts", "./node_modules/@types/lodash/common/seq.d.ts", "./node_modules/@types/lodash/common/string.d.ts", "./node_modules/@types/lodash/common/util.d.ts", "./node_modules/@types/lodash/index.d.ts", "./node_modules/async-validator/dist-types/interface.d.ts", "./node_modules/async-validator/dist-types/index.d.ts", "./node_modules/element-plus/node_modules/@vueuse/shared/index.d.ts", "./node_modules/element-plus/node_modules/@vueuse/core/index.d.ts", "./node_modules/memoize-one/dist/memoize-one.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./node_modules/@ctrl/tinycolor/dist/interfaces.d.ts", "./node_modules/@ctrl/tinycolor/dist/index.d.ts", "./node_modules/@ctrl/tinycolor/dist/css-color-names.d.ts", "./node_modules/@ctrl/tinycolor/dist/readability.d.ts", "./node_modules/@ctrl/tinycolor/dist/to-ms-filter.d.ts", "./node_modules/@ctrl/tinycolor/dist/from-ratio.d.ts", "./node_modules/@ctrl/tinycolor/dist/format-input.d.ts", "./node_modules/@ctrl/tinycolor/dist/random.d.ts", "./node_modules/@ctrl/tinycolor/dist/conversion.d.ts", "./node_modules/@ctrl/tinycolor/dist/public_api.d.ts", "./node_modules/element-plus/es/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/add-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/aim.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/alarm-clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/apple.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/avatar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/back.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/baseball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/basketball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bicycle.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bowl.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/briefcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/burger.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/calendar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cellphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cherry.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chicken.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chrome-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee-cup.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coin.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cold-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/comment.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/compass.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/connection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coordinate.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/copy-document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cpu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/credit-card.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/crop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-caret.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-analysis.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-board.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dessert.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/discount.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish-dot.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-copy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/download.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/drizzling.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit-pen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/element-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/expand.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/failed.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/female.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/files.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/film.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/filter.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/finished.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/first-aid-kit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/flag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-opened.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/food.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/football.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fork-spoon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fries.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/full-screen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/gold-medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grape.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grid.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/guide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/handbag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/headset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/histogram.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/home-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hot-water.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/house.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/info-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/iphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/key.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/knife-fork.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lightning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/link.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/list.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/loading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-information.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lollipop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magic-stick.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magnet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/male.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/management.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/map-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/memo.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/menu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mic.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/microphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/milk-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/minus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/money.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/monitor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon-night.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mostly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mouse.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mug.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute-notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/no-smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notebook.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/odometer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/office-building.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/open.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/operation.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/opportunity.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/orange.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/paperclip.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/partly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pear.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-rounded.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pie-chart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/place.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/platform.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pointer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/position.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/postcard.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pouring.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/present.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/price-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/printer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/promotion.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/quartz-watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/question-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/rank.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading-lamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refrigerator.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scale-to-original.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/school.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scissor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/search.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/semi-select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/service.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/set-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/setting.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/share.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ship.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-bag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-trolley.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/soccer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sold-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stopwatch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/success-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sugar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunny.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunrise.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-button.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/takeaway-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ticket.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tickets.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/timer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/toilet-paper.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tools.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trend-charts.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy-base.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/turn-off.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/umbrella.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/unlock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/van.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-pause.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-play.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/view.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warn-triangle-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watermelon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wind-power.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-in.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/index.d.ts", "./node_modules/dayjs/plugin/relativetime.d.ts", "./src/components/historydialog.vue.ts", "./src/components/settingsdialog.vue.ts", "./src/components/statusindicator.vue.ts", "./src/components/themetoggle.vue.ts", "./src/components/watermark.vue.ts", "./src/components/weightwebsocketstatus.vue.ts", "./node_modules/axios/index.d.ts", "./src/services/api.ts", "./src/stores/device.ts", "./node_modules/@vueuse/shared/index.d.mts", "./node_modules/@vueuse/core/index.d.mts", "./src/layout/components/headerbar.vue.ts", "./src/layout/components/sidebar.vue.ts", "./src/layout/components/globalnotification.vue.ts", "./src/stores/layout.ts", "./src/layout/index.vue.ts", "./src/views/watermarktest.vue.ts", "./src/views/boot/index.vue.ts", "./node_modules/flv.js/d.ts/flv.d.ts", "./src/config/video.ts", "./src/utils/tokenmanager.ts", "./src/views/dashboard/components/videomonitor.vue.ts", "./node_modules/echarts/types/dist/echarts.d.ts", "./node_modules/echarts/index.d.ts", "./src/views/dashboard/components/weightdisplay.vue.ts", "./src/views/dashboard/components/plateinputcard.vue.ts", "./src/views/dashboard/components/actionpanel.vue.ts", "./src/utils/timeformatter.ts", "./src/views/dashboard/components/recentrecordstable.vue.ts", "./src/views/dashboard/index.vue.ts", "./src/views/debug/websockettest.vue.ts", "./src/views/error/notfound.vue.ts", "./src/views/history/index.vue.ts", "./src/views/loading/index.vue.ts", "./src/views/monitor/index.vue.ts", "./src/views/settings/index.vue.ts", "./src/views/systemboot/index.vue.ts", "./src/views/terminal/dualcameraterminal.backup.vue.ts", "./src/views/terminal/dualcameraterminal.vue.ts", "./src/views/terminal/index.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/router/index.ts", "./src/main.ts", "./src/composables/usergwebsocket.ts", "./node_modules/element-plus/global.d.ts", "./src/views/error/notfound.vue"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0"], "root": [[59, 64], [399, 404], 406, 407, [410, 416], [418, 420], [423, 439], [446, 449]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 58, 65, 91, 450], [56, 58, 65, 91, 445, 450], [52], [92], [93], [92, 93, 94, 95, 96, 97, 98, 99, 100], [56, 58, 65, 91, 450], [103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395], [396], [87], [88, 89], [69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [69, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [69, 70, 71, 73, 74, 75, 76, 77, 78, 79, 80, 81], [69, 70, 71, 72, 74, 75, 76, 77, 78, 79, 80, 81], [69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81], [69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81], [69, 70, 71, 72, 73, 74, 75, 77, 78, 79, 80, 81], [69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 80, 81], [69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81], [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81], [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81], [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [57, 408], [57], [82], [67], [66], [68], [421], [49, 56, 58, 65, 68, 81, 83, 85, 86, 88, 90, 91, 101, 450], [56, 58, 65, 91, 102], [57, 84], [56, 57, 65, 91, 450], [444], [440], [441], [442, 443], [50, 55], [50], [51, 56, 58, 59, 60, 61, 62, 63, 65, 91, 450], [51, 56, 58, 65, 91, 450], [51, 56, 58, 65, 68, 91, 102, 397, 398, 450], [51, 56, 58, 65, 91, 102, 450], [51, 56, 58, 59, 65, 91, 450], [51, 56, 58, 62, 65, 91, 450], [51, 56, 58, 62, 65, 91, 102, 450], [51], [51, 56, 58, 65, 68, 91, 401, 402, 407, 409, 450], [51, 56, 58, 65, 91, 410, 411, 412, 413, 450], [51, 56, 58, 59, 64, 65, 91, 102, 397, 445, 447, 450], [51, 59, 91, 415, 416, 430, 432, 435, 437, 445], [51, 102, 405], [51, 58, 102, 406], [51, 58], [51, 67, 68, 398], [51, 405, 418], [51, 56, 58, 65, 68, 91, 406, 450], [51, 56, 58, 62, 65, 91, 406, 450], [51, 56, 58, 62, 65, 68, 91, 102, 397, 406, 426, 450], [51, 56, 58, 65, 91, 397, 405, 406, 409, 417, 418, 419, 450], [51, 56, 58, 62, 65, 91, 406, 422, 450], [51, 56, 58, 62, 65, 91, 407, 420, 423, 424, 425, 427, 450], [51, 56, 58, 62, 65, 91, 404, 450], [51, 56, 58, 65, 68, 91, 397, 398, 406, 450], [51, 56, 58, 65, 91, 401, 450], [51, 56, 58, 59, 65, 68, 91, 413, 450], [51, 56, 58, 65, 67, 68, 91, 397, 450], [51, 56, 58, 62, 65, 68, 91, 102, 397, 399, 400, 401, 402, 407, 450], [51, 56, 58, 62, 65, 67, 68, 91, 102, 397, 399, 400, 401, 402, 407, 426, 450], [51, 56, 58, 62, 65, 68, 91, 397, 399, 400, 401, 402, 407, 450], [51, 56, 58, 60, 63, 65, 91, 403, 450], [48, 50, 51, 56, 58, 65, 91], [56, 58, 65, 91], [49, 56, 58, 65, 68, 81, 83, 85, 86, 88, 90, 91, 101], [449], [56, 57, 65, 91], [48, 50, 51, 56, 58, 65, 91, 451], [51, 56, 58, 65, 91], [448]], "referencedMap": [[439, 1], [446, 2], [53, 3], [100, 4], [98, 4], [97, 5], [93, 4], [101, 6], [99, 5], [95, 5], [96, 5], [103, 7], [104, 7], [105, 7], [106, 7], [107, 7], [108, 7], [109, 7], [110, 7], [111, 7], [112, 7], [113, 7], [114, 7], [115, 7], [116, 7], [117, 7], [118, 7], [119, 7], [120, 7], [121, 7], [122, 7], [123, 7], [124, 7], [125, 7], [126, 7], [127, 7], [128, 7], [129, 7], [130, 7], [131, 7], [132, 7], [133, 7], [134, 7], [135, 7], [136, 7], [137, 7], [138, 7], [139, 7], [140, 7], [141, 7], [142, 7], [143, 7], [144, 7], [145, 7], [146, 7], [147, 7], [148, 7], [149, 7], [150, 7], [151, 7], [152, 7], [153, 7], [154, 7], [155, 7], [156, 7], [157, 7], [158, 7], [159, 7], [160, 7], [161, 7], [162, 7], [163, 7], [164, 7], [165, 7], [166, 7], [167, 7], [168, 7], [169, 7], [170, 7], [171, 7], [172, 7], [173, 7], [174, 7], [175, 7], [176, 7], [177, 7], [178, 7], [179, 7], [180, 7], [181, 7], [182, 7], [183, 7], [184, 7], [185, 7], [186, 7], [187, 7], [188, 7], [189, 7], [190, 7], [191, 7], [192, 7], [193, 7], [194, 7], [195, 7], [196, 7], [197, 7], [198, 7], [199, 7], [200, 7], [201, 7], [202, 7], [203, 7], [204, 7], [205, 7], [206, 7], [207, 7], [208, 7], [209, 7], [210, 7], [211, 7], [212, 7], [213, 7], [214, 7], [215, 7], [216, 7], [217, 7], [218, 7], [219, 7], [220, 7], [221, 7], [222, 7], [223, 7], [224, 7], [225, 7], [226, 7], [227, 7], [228, 7], [229, 7], [230, 7], [231, 7], [232, 7], [233, 7], [234, 7], [235, 7], [236, 7], [237, 7], [238, 7], [239, 7], [240, 7], [241, 7], [242, 7], [243, 7], [244, 7], [396, 8], [245, 7], [246, 7], [247, 7], [248, 7], [249, 7], [250, 7], [251, 7], [252, 7], [253, 7], [254, 7], [255, 7], [256, 7], [257, 7], [258, 7], [259, 7], [260, 7], [261, 7], [262, 7], [263, 7], [264, 7], [265, 7], [266, 7], [267, 7], [268, 7], [269, 7], [270, 7], [271, 7], [272, 7], [273, 7], [274, 7], [275, 7], [276, 7], [277, 7], [278, 7], [279, 7], [280, 7], [281, 7], [282, 7], [283, 7], [284, 7], [285, 7], [286, 7], [287, 7], [288, 7], [289, 7], [290, 7], [291, 7], [292, 7], [293, 7], [294, 7], [295, 7], [296, 7], [297, 7], [298, 7], [299, 7], [300, 7], [301, 7], [302, 7], [303, 7], [304, 7], [305, 7], [306, 7], [307, 7], [308, 7], [309, 7], [310, 7], [311, 7], [312, 7], [313, 7], [314, 7], [315, 7], [316, 7], [317, 7], [318, 7], [319, 7], [320, 7], [321, 7], [322, 7], [323, 7], [324, 7], [325, 7], [326, 7], [327, 7], [328, 7], [329, 7], [330, 7], [331, 7], [332, 7], [333, 7], [334, 7], [335, 7], [336, 7], [337, 7], [338, 7], [339, 7], [340, 7], [341, 7], [342, 7], [343, 7], [344, 7], [345, 7], [346, 7], [347, 7], [348, 7], [349, 7], [350, 7], [351, 7], [352, 7], [353, 7], [354, 7], [355, 7], [356, 7], [357, 7], [358, 7], [359, 7], [360, 7], [361, 7], [362, 7], [363, 7], [364, 7], [365, 7], [366, 7], [367, 7], [368, 7], [369, 7], [370, 7], [371, 7], [372, 7], [373, 7], [374, 7], [375, 7], [376, 7], [377, 7], [378, 7], [379, 7], [380, 7], [381, 7], [382, 7], [383, 7], [384, 7], [385, 7], [386, 7], [387, 7], [388, 7], [389, 7], [390, 7], [391, 7], [392, 7], [393, 7], [394, 7], [395, 7], [397, 9], [88, 10], [90, 11], [70, 12], [71, 13], [69, 14], [72, 15], [73, 16], [74, 17], [75, 18], [76, 19], [77, 20], [78, 21], [79, 22], [80, 23], [81, 24], [54, 25], [55, 26], [47, 27], [48, 28], [50, 29], [409, 30], [408, 31], [83, 32], [68, 33], [67, 34], [398, 35], [422, 36], [102, 37], [65, 7], [450, 38], [85, 39], [84, 31], [58, 40], [445, 41], [441, 42], [442, 43], [444, 44], [57, 7], [91, 7], [56, 45], [51, 46], [64, 47], [63, 48], [399, 49], [400, 50], [401, 48], [402, 51], [403, 48], [404, 52], [61, 48], [449, 53], [418, 54], [412, 48], [410, 55], [411, 48], [414, 56], [448, 57], [447, 58], [406, 59], [62, 48], [407, 60], [413, 61], [59, 61], [60, 48], [426, 62], [419, 63], [416, 48], [425, 64], [424, 65], [427, 66], [420, 67], [423, 68], [428, 69], [429, 70], [430, 48], [431, 71], [432, 64], [433, 72], [434, 73], [435, 74], [436, 75], [437, 76], [438, 77], [415, 78]], "exportedModulesMap": [[439, 1], [446, 79], [53, 3], [100, 4], [98, 4], [97, 5], [93, 4], [101, 6], [99, 5], [95, 5], [96, 5], [103, 80], [104, 80], [105, 80], [106, 80], [107, 80], [108, 80], [109, 80], [110, 80], [111, 80], [112, 80], [113, 80], [114, 80], [115, 80], [116, 80], [117, 80], [118, 80], [119, 80], [120, 80], [121, 80], [122, 80], [123, 80], [124, 80], [125, 80], [126, 80], [127, 80], [128, 80], [129, 80], [130, 80], [131, 80], [132, 80], [133, 80], [134, 80], [135, 80], [136, 80], [137, 80], [138, 80], [139, 80], [140, 80], [141, 80], [142, 80], [143, 80], [144, 80], [145, 80], [146, 80], [147, 80], [148, 80], [149, 80], [150, 80], [151, 80], [152, 80], [153, 80], [154, 80], [155, 80], [156, 80], [157, 80], [158, 80], [159, 80], [160, 80], [161, 80], [162, 80], [163, 80], [164, 80], [165, 80], [166, 80], [167, 80], [168, 80], [169, 80], [170, 80], [171, 80], [172, 80], [173, 80], [174, 80], [175, 80], [176, 80], [177, 80], [178, 80], [179, 80], [180, 80], [181, 80], [182, 80], [183, 80], [184, 80], [185, 80], [186, 80], [187, 80], [188, 80], [189, 80], [190, 80], [191, 80], [192, 80], [193, 80], [194, 80], [195, 80], [196, 80], [197, 80], [198, 80], [199, 80], [200, 80], [201, 80], [202, 80], [203, 80], [204, 80], [205, 80], [206, 80], [207, 80], [208, 80], [209, 80], [210, 80], [211, 80], [212, 80], [213, 80], [214, 80], [215, 80], [216, 80], [217, 80], [218, 80], [219, 80], [220, 80], [221, 80], [222, 80], [223, 80], [224, 80], [225, 80], [226, 80], [227, 80], [228, 80], [229, 80], [230, 80], [231, 80], [232, 80], [233, 80], [234, 80], [235, 80], [236, 80], [237, 80], [238, 80], [239, 80], [240, 80], [241, 80], [242, 80], [243, 80], [244, 80], [396, 8], [245, 80], [246, 80], [247, 80], [248, 80], [249, 80], [250, 80], [251, 80], [252, 80], [253, 80], [254, 80], [255, 80], [256, 80], [257, 80], [258, 80], [259, 80], [260, 80], [261, 80], [262, 80], [263, 80], [264, 80], [265, 80], [266, 80], [267, 80], [268, 80], [269, 80], [270, 80], [271, 80], [272, 80], [273, 80], [274, 80], [275, 80], [276, 80], [277, 80], [278, 80], [279, 80], [280, 80], [281, 80], [282, 80], [283, 80], [284, 80], [285, 80], [286, 80], [287, 80], [288, 80], [289, 80], [290, 80], [291, 80], [292, 80], [293, 80], [294, 80], [295, 80], [296, 80], [297, 80], [298, 80], [299, 80], [300, 80], [301, 80], [302, 80], [303, 80], [304, 80], [305, 80], [306, 80], [307, 80], [308, 80], [309, 80], [310, 80], [311, 80], [312, 80], [313, 80], [314, 80], [315, 80], [316, 80], [317, 80], [318, 80], [319, 80], [320, 80], [321, 80], [322, 80], [323, 80], [324, 80], [325, 80], [326, 80], [327, 80], [328, 80], [329, 80], [330, 80], [331, 80], [332, 80], [333, 80], [334, 80], [335, 80], [336, 80], [337, 80], [338, 80], [339, 80], [340, 80], [341, 80], [342, 80], [343, 80], [344, 80], [345, 80], [346, 80], [347, 80], [348, 80], [349, 80], [350, 80], [351, 80], [352, 80], [353, 80], [354, 80], [355, 80], [356, 80], [357, 80], [358, 80], [359, 80], [360, 80], [361, 80], [362, 80], [363, 80], [364, 80], [365, 80], [366, 80], [367, 80], [368, 80], [369, 80], [370, 80], [371, 80], [372, 80], [373, 80], [374, 80], [375, 80], [376, 80], [377, 80], [378, 80], [379, 80], [380, 80], [381, 80], [382, 80], [383, 80], [384, 80], [385, 80], [386, 80], [387, 80], [388, 80], [389, 80], [390, 80], [391, 80], [392, 80], [393, 80], [394, 80], [395, 80], [397, 9], [88, 10], [90, 11], [70, 12], [71, 13], [69, 14], [72, 15], [73, 16], [74, 17], [75, 18], [76, 19], [77, 20], [78, 21], [79, 22], [80, 23], [81, 24], [54, 25], [55, 26], [47, 27], [48, 28], [50, 29], [409, 30], [408, 31], [83, 32], [68, 33], [67, 34], [398, 35], [422, 36], [102, 81], [65, 80], [450, 82], [85, 39], [84, 31], [58, 83], [445, 79], [441, 79], [440, 84], [442, 79], [443, 79], [444, 79], [57, 80], [91, 80], [56, 45], [51, 46], [64, 47], [63, 48], [399, 49], [400, 50], [401, 48], [402, 51], [403, 48], [404, 52], [61, 85], [449, 86], [418, 54], [412, 48], [410, 55], [411, 48], [414, 56], [447, 79], [406, 59], [62, 85], [407, 61], [413, 61], [59, 61], [60, 85], [426, 62], [419, 63], [416, 48], [425, 64], [424, 65], [427, 66], [420, 67], [423, 68], [428, 69], [429, 70], [430, 48], [431, 71], [432, 64], [433, 72], [434, 73], [435, 74], [436, 75], [437, 76], [438, 77], [415, 78]], "semanticDiagnosticsPerFile": [439, 446, 53, 52, 100, 94, 98, 97, 93, 92, 101, 99, 95, 96, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 396, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 397, 88, 90, 87, 89, 70, 71, 69, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 54, 55, 47, 48, 50, 46, 409, 408, 83, 82, 405, 49, 68, 67, 66, 398, 422, 421, 102, 65, 450, 85, 84, 417, 86, 58, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 445, 441, 440, 442, 443, 444, 57, 91, 56, 51, 64, 63, [399, [{"file": "./src/components/historydialog.vue", "start": 10524, "length": 17, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: number; plateNumber: string; weight: number; maxWeight: number; vehicleStatus: string; status: string; operator: string; weighTime: Date; images: string[]; }[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: number; plateNumber: string; weight: number; maxWeight: number; vehicleStatus: string; status: string; operator: string; weighTime: Date; images: string[]; }' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}, {"file": "./src/components/historydialog.vue", "start": 10800, "length": 17, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'."}, {"file": "./src/components/historydialog.vue", "start": 10903, "length": 15, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'."}, {"file": "./src/components/historydialog.vue", "start": 10933, "length": 17, "code": 2345, "category": 1, "messageText": "Argument of type 'number' is not assignable to parameter of type 'string'."}, {"file": "./src/components/historydialog.vue", "start": 11667, "length": 15, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ normal: string; overweight: string; error: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ normal: string; overweight: string; error: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/components/historydialog.vue", "start": 11838, "length": 15, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ normal: string; overweight: string; error: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ normal: string; overweight: string; error: string; }'.", "category": 1, "code": 7054}]}}]], [400, [{"file": "./src/components/settingsdialog.vue", "start": 1617, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}]], [401, [{"file": "./src/components/statusindicator.vue", "start": 785, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/components/statusindicator.vue", "start": 859, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}]], [402, [{"file": "./src/components/themetoggle.vue", "start": 440, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/components/themetoggle.vue", "start": 540, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/components/themetoggle.vue", "start": 704, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}]], 403, [404, [{"file": "./src/components/weightwebsocketstatus.vue", "start": 1767, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 1799, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 1843, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 2153, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 2584, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 2645, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/components/weightwebsocketstatus.vue", "start": 2795, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}]], 61, 449, 418, 412, [410, [{"file": "./src/layout/components/headerbar.vue", "start": 2089, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/headerbar.vue", "start": 2155, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/headerbar.vue", "start": 2326, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/headerbar.vue", "start": 2439, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/headerbar.vue", "start": 2468, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/headerbar.vue", "start": 2646, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}]], [411, [{"file": "./src/layout/components/sidebar.vue", "start": 1418, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/layout/components/sidebar.vue", "start": 1798, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}]], [414, [{"file": "./src/layout/index.vue", "start": 996, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}]], 448, [447, [{"file": "./src/router/index.ts", "start": 181, "length": 11, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'nprogress'. 'D:/code/serialPort/new-frontend/node_modules/nprogress/nprogress.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "nprogress"}}]}}]], [406, [{"file": "./src/services/api.ts", "start": 392, "length": 33, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '(config: AxiosRequestConfig) => AxiosRequestConfig<any>' is not assignable to parameter of type '(value: InternalAxiosRequestConfig<any>) => InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'headers' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'AxiosHeaders | (Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }> & Partial<...>) | undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }>'.", "category": 1, "code": 2322}]}]}]}]}]}]}}]], 62, 407, 413, 59, 60, 426, [419, [{"file": "./src/utils/tokenmanager.ts", "start": 592, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}]], 416, [425, [{"file": "./src/views/dashboard/components/actionpanel.vue", "start": 2719, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 2753, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 2788, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 2833, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 2896, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 3065, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 3202, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 3406, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 3544, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 4052, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 4242, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 4542, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 4733, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 4857, "length": 12, "messageText": "Cannot find name 'ElMessageBox'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 5070, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 5198, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 5303, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 5894, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 6048, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/actionpanel.vue", "start": 1146, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(status: 'IN' | 'OUT') => void' is not assignable to type '(val: string | number | boolean | undefined) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'status' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean | undefined' is not assignable to type '\"IN\" | \"OUT\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type '\"IN\" | \"OUT\"'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}]], [424, [{"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 4859, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 4936, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 4987, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 5018, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 5054, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 5593, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 5765, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 5904, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 6033, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 6165, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 6427, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 6565, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 6827, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 7807, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 7861, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 8971, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 9229, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 9284, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 9740, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 10086, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 10244, "length": 11, "messageText": "Cannot find name 'onUnmounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/plateinputcard.vue", "start": 1210, "length": 0, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(mode: string) => void' is not assignable to type '(name: TabPaneName) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'mode' and 'name' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TabPaneName' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}]], [427, [{"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 9420, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'sort' does not exist on type 'VehicleHistoryResponse'."}, {"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 9426, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 9429, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 9661, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 11685, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}, {"file": "./src/views/dashboard/components/recentrecordstable.vue", "start": 11963, "length": 16, "code": 2345, "category": 1, "messageText": "Argument of type '\"VEHICLE_RECORD\"' is not assignable to parameter of type 'MessageType'."}]], [420, [{"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6543, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6633, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6675, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6723, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6774, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6832, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6883, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6918, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6954, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 6999, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 7125, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 7255, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 7466, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 7679, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 7745, "length": 11, "messageText": "Cannot find name 'onUnmounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 8949, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 9257, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 10757, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 12882, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 13165, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 13494, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 14262, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 15285, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 15421, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 16343, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 16477, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 17010, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 17210, "length": 6, "messageText": "Parameter 'player' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 17218, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 17449, "length": 6, "messageText": "Parameter 'camera' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/videomonitor.vue", "start": 5007, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(index?: number | undefined, retryCount?: number) => Promise<void>' is not assignable to type '(evt: MouseEvent) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'index' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'MouseEvent' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}]], [423, [{"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2518, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2543, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2574, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2604, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2630, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2660, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2732, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 2790, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 3087, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 3274, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 3642, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 3997, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 4339, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 4750, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 4924, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 4982, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 5441, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 6107, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 6861, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 6941, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 7484, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 8278, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 8374, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 8530, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 9309, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 9348, "length": 8, "messageText": "Cannot find name 'nextTick'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/components/weightdisplay.vue", "start": 9813, "length": 11, "messageText": "Cannot find name 'onUnmounted'.", "category": 1, "code": 2304}]], [428, [{"file": "./src/views/dashboard/index.vue", "start": 1195, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/dashboard/index.vue", "start": 1289, "length": 15, "code": 2345, "category": 1, "messageText": "Argument of type '\"DEVICE_STATUS\"' is not assignable to parameter of type 'MessageType'."}]], [429, [{"file": "./src/views/debug/websockettest.vue", "start": 4800, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 4849, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 4891, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 4921, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 4959, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 4992, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5027, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5062, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5096, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5134, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5189, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5227, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5263, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5304, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 5340, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 6034, "length": 8, "messageText": "Cannot find name 'nextTick'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 8917, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/debug/websockettest.vue", "start": 1212, "length": 5, "code": 2322, "category": 1, "messageText": "Type '\"\" | \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 755439, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [430, [{"file": "./src/views/error/notfound.vue", "start": 1741, "length": 9, "messageText": "Cannot find name 'use<PERSON><PERSON>er'.", "category": 1, "code": 2304}]], [431, [{"file": "./src/views/history/index.vue", "start": 6332, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 6441, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 6483, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 6523, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 6621, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 6655, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 7374, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 7665, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 7907, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 8034, "length": 12, "messageText": "Cannot find name 'ElMessageBox'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 8207, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 8278, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/history/index.vue", "start": 9202, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}]], [432, [{"file": "./src/views/loading/index.vue", "start": 1576, "length": 9, "messageText": "Cannot find name 'use<PERSON><PERSON>er'.", "category": 1, "code": 2304}, {"file": "./src/views/loading/index.vue", "start": 1624, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/loading/index.vue", "start": 1652, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/loading/index.vue", "start": 3048, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}]], 433, [434, [{"file": "./src/views/settings/index.vue", "start": 9124, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9171, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9223, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/settings/index.vue", "start": 9302, "length": 8, "messageText": "Cannot find name 'computed'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9366, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/settings/index.vue", "start": 9456, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9499, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9673, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 9892, "length": 8, "messageText": "Cannot find name 'reactive'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10328, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10464, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10558, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10660, "length": 12, "messageText": "Cannot find name 'ElMessageBox'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10830, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10885, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 10984, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 11154, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 11210, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/settings/index.vue", "start": 6245, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(theme: string) => void' is not assignable to type '(val: string | number | boolean | undefined) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'theme' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/views/settings/index.vue", "start": 6610, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(collapsed: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'collapsed' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}]], [435, [{"file": "./src/views/systemboot/index.vue", "start": 13337, "length": 3, "code": 2345, "category": 1, "messageText": "Argument of type '{ id: number; timestamp: number; message: string; level: string; status: string; }' is not assignable to parameter of type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 13425, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'scrollTop' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 13457, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'scrollHeight' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 13880, "length": 6, "messageText": "Parameter 'device' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/systemboot/index.vue", "start": 16998, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'webkitRequestFullscreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17040, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'webkitRequestFullscreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17090, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'mozRequestFullScreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17129, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'mozRequestFullScreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17176, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'msRequestFullscreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17214, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'msRequestFullscreen' does not exist on type 'HTMLElement'. Did you mean 'requestFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 316516, "length": 62, "messageText": "'requestFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17458, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'webkitExitFullscreen' does not exist on type 'Document'. Did you mean 'exitFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 287417, "length": 32, "messageText": "'exitFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17498, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'webkitExitFullscreen' does not exist on type 'Document'. Did you mean 'exitFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 287417, "length": 32, "messageText": "'exitFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17546, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'mozCancelFullScreen' does not exist on type 'Document'."}, {"file": "./src/views/systemboot/index.vue", "start": 17585, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'mozCancelFullScreen' does not exist on type 'Document'."}, {"file": "./src/views/systemboot/index.vue", "start": 17632, "length": 16, "code": 2551, "category": 1, "messageText": "Property 'msExitFullscreen' does not exist on type 'Document'. Did you mean 'exitFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 287417, "length": 32, "messageText": "'exitFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17668, "length": 16, "code": 2551, "category": 1, "messageText": "Property 'msExitFullscreen' does not exist on type 'Document'. Did you mean 'exitFullscreen'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 287417, "length": 32, "messageText": "'exitFullscreen' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17871, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'webkitFullscreenElement' does not exist on type 'Document'. Did you mean 'fullscreenElement'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 298212, "length": 17, "messageText": "'fullscreenElement' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17920, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'mozFullScreenElement' does not exist on type 'Document'. Did you mean 'fullscreenElement'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 298212, "length": 17, "messageText": "'fullscreenElement' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 17966, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'msFullscreenElement' does not exist on type 'Document'. Did you mean 'fullscreenElement'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 298212, "length": 17, "messageText": "'fullscreenElement' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/views/systemboot/index.vue", "start": 7128, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7208, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7298, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7371, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'message' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7478, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7565, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'never'."}, {"file": "./src/views/systemboot/index.vue", "start": 7650, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'never'."}]], [436, [{"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 23938, "length": 25, "code": 2322, "category": 1, "messageText": "Type '{ id: number; camera: \"entrance\" | \"exit\"; cameraName: string; thumbnail: string; fullUrl: string; timestamp: number; plateNumber: string | null; }' is not assignable to type 'null'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 26629, "length": 171, "code": 2345, "category": 1, "messageText": "Argument of type '{ id: number; plateNumber: string; weight: number; status: \"IN\" | \"OUT\"; timestamp: number; }' is not assignable to parameter of type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 489, "length": 7, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"error\" | \"offline\" | \"online\" | \"warning\"'.", "relatedInformation": [{"file": "./src/components/statusindicator.vue", "start": 565, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ showPulse: boolean; }> & Omit<{ readonly label: string; readonly status: \"error\" | \"offline\" | \"online\" | \"warning\"; readonly icon: string; readonly showPulse: boolean; } & VNodeProps & AllowedComponentProps & ComponentCustomProps, \"showPulse\"> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 7335, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'thumbnail' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 7494, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 7569, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 7652, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 8941, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'thumbnail' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 9096, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 9167, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 9246, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 10078, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 755439, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16076, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'fullUrl' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16100, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'thumbnail' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16130, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'cameraName' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16239, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'cameraName' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16324, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.backup.vue", "start": 16393, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}]], [437, [{"file": "./src/views/terminal/dualcameraterminal.vue", "start": 36327, "length": 25, "code": 2322, "category": 1, "messageText": "Type '{ id: number; camera: \"entrance\" | \"exit\"; cameraName: string; thumbnail: string; fullUrl: string; timestamp: number; plateNumber: string | null; }' is not assignable to type 'null'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 489, "length": 7, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"error\" | \"offline\" | \"online\" | \"warning\"'.", "relatedInformation": [{"file": "./src/components/statusindicator.vue", "start": 565, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ showPulse: boolean; }> & Omit<{ readonly label: string; readonly status: \"error\" | \"offline\" | \"online\" | \"warning\"; readonly icon: string; readonly showPulse: boolean; } & VNodeProps & AllowedComponentProps & ComponentCustomProps, \"showPulse\"> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 7289, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'thumbnail' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 7448, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 7523, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 7606, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 8853, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'thumbnail' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 9008, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 9079, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 9158, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'plateNumber' does not exist on type 'never'."}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 10067, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 755439, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/terminal/dualcameraterminal.vue", "start": 18120, "length": 7, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"error\" | \"offline\" | \"online\" | \"warning\"'.", "relatedInformation": [{"file": "./src/components/statusindicator.vue", "start": 565, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ showPulse: boolean; }> & Omit<{ readonly label: string; readonly status: \"error\" | \"offline\" | \"online\" | \"warning\"; readonly icon: string; readonly showPulse: boolean; } & VNodeProps & AllowedComponentProps & ComponentCustomProps, \"showPulse\"> & Record<...>'", "category": 3, "code": 6500}]}]], [438, [{"file": "./src/views/terminal/index.vue", "start": 13078, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 13176, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 13265, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 13330, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 14295, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 14378, "length": 171, "code": 2345, "category": 1, "messageText": "Argument of type '{ id: number; plateNumber: string; weight: number; status: \"IN\" | \"OUT\"; timestamp: number; }' is not assignable to parameter of type 'never'."}, {"file": "./src/views/terminal/index.vue", "start": 14615, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 14837, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 14893, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 15116, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 15176, "length": 9, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/views/terminal/index.vue", "start": 486, "length": 7, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"error\" | \"offline\" | \"online\" | \"warning\"'.", "relatedInformation": [{"file": "./src/components/statusindicator.vue", "start": 565, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ showPulse: boolean; }> & Omit<{ readonly label: string; readonly status: \"error\" | \"offline\" | \"online\" | \"warning\"; readonly icon: string; readonly showPulse: boolean; } & VNodeProps & AllowedComponentProps & ComponentCustomProps, \"showPulse\"> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/terminal/index.vue", "start": 1461, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'toggleAllCameras' does not exist on type '{ $: ComponentInternalInstance; $data: {}; $props: Partial<{}> & Omit<{} & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>; ... 11 more ...; $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (args_0: R, args_1: R, args_2: OnCleanup) => any : (ar...'."}, {"file": "./src/views/terminal/index.vue", "start": 1500, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'allCamerasPlaying' does not exist on type '{ $: ComponentInternalInstance; $data: {}; $props: Partial<{}> & Omit<{} & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>; ... 11 more ...; $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (args_0: R, args_1: R, args_2: OnCleanup) => any : (ar...'."}, {"file": "./src/views/terminal/index.vue", "start": 1614, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'captureAllPhotos' does not exist on type '{ $: ComponentInternalInstance; $data: {}; $props: Partial<{}> & Omit<{} & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>; ... 11 more ...; $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (args_0: R, args_1: R, args_2: OnCleanup) => any : (ar...'."}, {"file": "./src/views/terminal/index.vue", "start": 2937, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 755439, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"info\" | \"warning\" | \"success\" | \"primary\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], 415], "affectedFilesPendingEmit": [64, 63, 399, 400, 401, 402, 403, 404, 61, 449, 418, 412, 410, 411, 414, 448, 447, 406, 62, 407, 413, 59, 60, 426, 419, 416, 425, 424, 427, 420, 423, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 415], "emitSignatures": [59, 60, 61, 62, 63, 64, 399, 400, 401, 402, 403, 404, 406, 407, 410, 411, 412, 413, 414, 415, 416, 418, 419, 420, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438]}, "version": "5.3.3"}