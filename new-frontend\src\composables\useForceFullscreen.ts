// 强制全屏组合式函数
// 作者：仕伟
// 专为一体机系统设计，确保应用始终保持全屏状态

import { ref, onMounted, onUnmounted } from 'vue'

export function useForceFullscreen() {
  const isForceFullscreen = ref(true) // 是否启用强制全屏模式
  let retryCount = 0
  const maxRetries = 5
  
  // 进入全屏
  const enterFullscreen = async (): Promise<boolean> => {
    try {
      const element = document.documentElement
      
      if (element.requestFullscreen) {
        await element.requestFullscreen({ navigationUI: 'show' })
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen()
      } else if ((element as any).mozRequestFullScreen) {
        await (element as any).mozRequestFullScreen()
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen()
      } else {
        return false
      }
      
      retryCount = 0 // 重置重试次数
      return true
    } catch (error) {
      // 重试机制
      if (retryCount < maxRetries) {
        retryCount++
        setTimeout(() => {
          enterFullscreen()
        }, 1000 * retryCount) // 递增延迟重试
      }
      
      return false
    }
  }
  
  // 检查是否全屏
  const isFullscreen = (): boolean => {
    return !!(
      document.fullscreenElement || 
      (document as any).webkitFullscreenElement || 
      (document as any).mozFullScreenElement || 
      (document as any).msFullscreenElement
    )
  }
  
  // 全屏状态变化处理
  const handleFullscreenChange = () => {
    const fullscreenState = isFullscreen()
    
    if (!fullscreenState && isForceFullscreen.value) {
      setTimeout(() => {
        if (!isFullscreen()) {
          enterFullscreen()
        }
      }, 2000)
    }
  }
  
  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (!document.hidden && !isFullscreen() && isForceFullscreen.value) {
      setTimeout(() => {
        if (!isFullscreen()) {
          enterFullscreen()
        }
      }, 500)
    }
  }
  
  // 键盘事件拦截
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isForceFullscreen.value) return
    
    // 拦截F11
    if (event.key === 'F11') {
      event.preventDefault()
      event.stopPropagation()
      if (!isFullscreen()) {
        enterFullscreen()
      }
      return false
    }
    
    // 拦截Escape（在全屏状态下）
    if (event.key === 'Escape' && isFullscreen()) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
    
    // 拦截Alt+F4
    if (event.altKey && event.key === 'F4') {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
    
    // 拦截Ctrl+W
    if (event.ctrlKey && (event.key === 'w' || event.key === 'W')) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }
  
  // 多重尝试进入全屏
  const forceEnterFullscreen = () => {
    const attempts = [100, 500, 1000, 2000, 3000]
    
    attempts.forEach((delay, index) => {
      setTimeout(() => {
        if (!isFullscreen() && isForceFullscreen.value) {
          enterFullscreen()
        }
      }, delay)
    })
  }
  
  // 定时检查全屏状态
  let statusCheckInterval: number | null = null
  
  const startStatusCheck = () => {
    if (statusCheckInterval) return
    
    statusCheckInterval = window.setInterval(() => {
      if (!isFullscreen() && !document.hidden && isForceFullscreen.value) {
        enterFullscreen()
      }
    }, 3000) // 每3秒检查一次
  }
  
  const stopStatusCheck = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval)
      statusCheckInterval = null
    }
  }
  
  // 初始化强制全屏
  const initForceFullscreen = () => {
    // 添加事件监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    document.addEventListener('keydown', handleKeyDown, { capture: true, passive: false })
    
    // 立即尝试进入全屏
    forceEnterFullscreen()
    
    // 开始状态检查
    startStatusCheck()
  }
  
  // 清理强制全屏
  const cleanupForceFullscreen = () => {
    // 移除事件监听器
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    document.removeEventListener('keydown', handleKeyDown, { capture: true })
    
    // 停止状态检查
    stopStatusCheck()
  }
  
  // 启用/禁用强制全屏模式
  const toggleForceFullscreen = (enabled: boolean) => {
    isForceFullscreen.value = enabled
    
    if (enabled) {
      forceEnterFullscreen()
      startStatusCheck()
    } else {
      stopStatusCheck()
    }
  }
  
  return {
    isForceFullscreen,
    enterFullscreen,
    isFullscreen,
    forceEnterFullscreen,
    initForceFullscreen,
    cleanupForceFullscreen,
    toggleForceFullscreen
  }
}