// RG设备专用WebSocket Hook
// 处理RG车辆识别、抓图、设备状态等消息
// 作者：仕伟

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWeightWebSocket } from '@/services/websocket'
import type { 
  RgVehicleRecognitionMessage, 
  RgSnapshotResultData, 
  RgDeviceStatusData,
  RgTrafficStatisticsData,
  RgCommandReplyData
} from '@/services/websocket'
import { ElMessage, ElNotification } from 'element-plus'

export function useRgWebSocket() {
  const wsService = useWeightWebSocket()
  
  // RG设备数据状态
  const latestPlateNumber = ref<string>('')
  const latestSnapshotPath = ref<string>('')
  const deviceStatusMap = ref<Map<string, RgDeviceStatusData>>(new Map())
  const trafficStats = ref<RgTrafficStatisticsData | null>(null)
  const commandReplies = ref<RgCommandReplyData[]>([])
  
  // 统计数据
  const vehicleRecognitionCount = ref(0)
  const snapshotCount = ref(0)
  const deviceCount = computed(() => deviceStatusMap.value.size)
  const onlineDeviceCount = computed(() => {
    let count = 0
    deviceStatusMap.value.forEach(status => {
      if (status.online) count++
    })
    return count
  })
  
  // 处理RG车辆识别消息
  const handleVehicleRecognition = (message: RgVehicleRecognitionMessage) => {
    console.log('🚗 收到RG车辆识别:', message)
    
    vehicleRecognitionCount.value++
    latestPlateNumber.value = message.data.decodedPlateNumber
    
    // 高优先级业务事件显示通知
    if (message.meta.priority === 'HIGH' && message.data.isBusinessEvent) {
      ElNotification({
        title: '车辆识别',
        message: `检测到车辆: ${message.data.decodedPlateNumber}`,
        type: 'success',
        position: 'top-right',
        duration: 3000
      })
    }
  }
  
  // 处理RG抓图结果
  const handleSnapshotResult = (data: { deviceSn: string, data: RgSnapshotResultData }) => {
    console.log('📸 收到RG抓图结果:', data)
    
    snapshotCount.value++
    
    if (data.data.success) {
      latestSnapshotPath.value = data.data.imagePath
      ElMessage.success(`设备 ${data.deviceSn} 抓图成功`)
    } else {
      ElMessage.error(`设备 ${data.deviceSn} 抓图失败: ${data.data.stateCode}`)
    }
  }
  
  // 处理RG设备状态
  const handleDeviceStatus = (data: { deviceSn: string, data: RgDeviceStatusData }) => {
    console.log('📡 收到RG设备状态:', data)
    
    deviceStatusMap.value.set(data.deviceSn, data.data)
    
    // 设备状态变化通知
    const statusText = data.data.online ? '上线' : '离线'
    const notificationType = data.data.online ? 'success' : 'warning'
    
    ElMessage({
      message: `设备 ${data.deviceSn} ${statusText}: ${data.data.description}`,
      type: notificationType,
      duration: 2000
    })
  }
  
  // 处理RG交通统计
  const handleTrafficStatistics = (data: { deviceSn: string, data: RgTrafficStatisticsData }) => {
    console.log('📊 收到RG交通统计:', data)
    
    trafficStats.value = data.data
    
    // 拥堵预警
    if (data.data.density === '拥堵') {
      ElNotification({
        title: '交通预警',
        message: `设备 ${data.deviceSn} 检测到拥堵，总车流量: ${data.data.totalCount}辆`,
        type: 'warning',
        position: 'top-right',
        duration: 5000
      })
    }
  }
  
  // 处理RG命令回执
  const handleCommandReply = (data: { deviceSn: string, data: RgCommandReplyData }) => {
    console.log('⚡ 收到RG命令回执:', data)
    
    commandReplies.value.unshift(data.data)
    
    // 限制数组长度
    if (commandReplies.value.length > 50) {
      commandReplies.value = commandReplies.value.slice(0, 50)
    }
    
    // 命令执行结果通知
    const resultText = data.data.success ? '成功' : '失败'
    const messageType = data.data.success ? 'success' : 'error'
    
    ElMessage({
      message: `设备 ${data.deviceSn} 命令 ${data.data.commandName} ${resultText}`,
      type: messageType,
      duration: 2000
    })
  }
  
  // 获取设备状态
  const getDeviceStatus = (deviceSn: string): RgDeviceStatusData | null => {
    return deviceStatusMap.value.get(deviceSn) || null
  }
  
  // 检查设备是否在线
  const isDeviceOnline = (deviceSn: string): boolean => {
    const status = getDeviceStatus(deviceSn)
    return status?.online || false
  }
  
  // 获取最近的命令回执
  const getRecentCommandReplies = (limit: number = 10): RgCommandReplyData[] => {
    return commandReplies.value.slice(0, limit)
  }
  
  // 清空统计数据
  const clearStats = () => {
    vehicleRecognitionCount.value = 0
    snapshotCount.value = 0
    latestPlateNumber.value = ''
    latestSnapshotPath.value = ''
    commandReplies.value = []
  }
  
  // 组件挂载时订阅所有RG消息
  onMounted(async () => {
    // 首先连接WebSocket
    await wsService.autoConnect()
    
    // 订阅RG设备相关消息
    wsService.subscribeRgVehicleRecognition(handleVehicleRecognition)
    wsService.subscribeRgSnapshotResult(handleSnapshotResult)
    wsService.subscribeRgDeviceStatus(handleDeviceStatus)
    wsService.subscribeRgTrafficStatistics(handleTrafficStatistics)
    wsService.subscribeRgCommandReply(handleCommandReply)
    
    console.log('🎯 RG设备WebSocket监听器已启动')
  })
  
  return {
    // WebSocket连接状态
    isConnected: wsService.isConnected,
    connectionStatus: wsService.connectionStatus,
    
    // RG设备数据
    latestPlateNumber,
    latestSnapshotPath,
    deviceStatusMap,
    trafficStats,
    commandReplies,
    
    // 统计信息
    vehicleRecognitionCount,
    snapshotCount,
    deviceCount,
    onlineDeviceCount,
    
    // 方法
    getDeviceStatus,
    isDeviceOnline,
    getRecentCommandReplies,
    clearStats,
    
    // WebSocket方法
    connect: wsService.connect,
    disconnect: wsService.disconnect
  }
}