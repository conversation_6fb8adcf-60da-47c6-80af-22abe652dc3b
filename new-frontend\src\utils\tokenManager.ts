// 基于旧项目方案的摄像头Token管理器
// 作者：仕伟

import axios from 'axios'
import type { CameraConfig } from '@/config/video'

/**
 * Token信息接口
 */
interface TokenInfo {
  token: string
  expireTime: number
  expiresIn: number
}

/**
 * Token响应接口（设备返回格式）
 */
interface DeviceTokenResponse {
  code: number
  message?: string
  body: {
    token: string
    expires_in?: number
    device_id?: string
  }
}

/**
 * 摄像头Token管理器
 * 基于旧项目的直接设备请求方案实现
 */
export class CameraTokenManager {
  private tokens = new Map<string, TokenInfo>()
  private refreshTimers = new Map<string, NodeJS.Timeout>()
  private refreshThreshold = 300000 // 提前5分钟刷新Token

  constructor() {
    this.setupCleanupHandler()
  }

  /**
   * 获取摄像头Token
   * @param cameraConfig 摄像头配置
   * @returns Promise<string> Token字符串
   */
  async getCameraToken(cameraConfig: CameraConfig): Promise<string> {
    const cacheKey = this.getCacheKey(cameraConfig)
    
    // 检查缓存的Token是否有效
    if (this.isTokenValid(cacheKey)) {
      console.log(`📋 使用缓存Token: ${cameraConfig.name}`)
      return this.tokens.get(cacheKey)!.token
    }

    try {
      const token = await this.fetchTokenFromDevice(cameraConfig)
      this.cacheToken(cacheKey, token, cameraConfig)
      this.scheduleTokenRefresh(cacheKey, cameraConfig)
      return token.token
    } catch (error) {
      console.error(`❌ ${cameraConfig.name} Token获取失败:`, error)
      throw error
    }
  }

  /**
   * 从摄像头设备获取Token
   * @param cameraConfig 摄像头配置
   * @returns Promise<TokenInfo>
   */
  private async fetchTokenFromDevice(cameraConfig: CameraConfig): Promise<TokenInfo> {
    const data = {
      type: "get_live_stream_type",
      module: "BUS_WEB_REQUEST"
    }

    const deviceUrl = `${cameraConfig.ip.startsWith('http') ? '' : 'http://'}${cameraConfig.ip}/request.php`
    
    try {
      console.log(`🔄 向设备请求Token: ${cameraConfig.name} (${cameraConfig.ip})`)
      
      const response = await axios.post<DeviceTokenResponse>(deviceUrl, data, {
        timeout: 8000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      if (response.data.code === 0) {
        const tokenData = response.data.body
        const expiresIn = tokenData.expires_in || 3600
        
        console.log(`✅ ${cameraConfig.name} Token获取成功`)
        console.log(`📅 Token过期时间: ${new Date(Date.now() + expiresIn * 1000).toLocaleString()}`)
        
        return {
          token: tokenData.token,
          expireTime: Date.now() + (expiresIn * 1000),
          expiresIn: expiresIn
        }
      } else {
        throw new Error(`设备返回错误: ${response.data.message || '未知错误'}`)
      }
    } catch (axiosError: any) {
      // 网络错误处理
      if (axiosError.code === 'ECONNREFUSED') {
        throw new Error(`无法连接到摄像头设备，请检查设备 ${cameraConfig.ip} 是否在线`)
      } else if (axiosError.code === 'ETIMEDOUT') {
        throw new Error(`连接摄像头设备 ${cameraConfig.ip} 超时`)
      } else if (axiosError.response?.status === 404) {
        throw new Error(`摄像头设备 ${cameraConfig.ip} 不支持Token接口 (/request.php)`)
      } else if (axiosError.response?.status >= 500) {
        throw new Error(`摄像头设备 ${cameraConfig.ip} 服务器内部错误`)
      } else {
        throw new Error(`网络请求失败: ${axiosError.message}`)
      }
    }
  }

  /**
   * 缓存Token
   * @param key 缓存键
   * @param tokenInfo Token信息
   * @param cameraConfig 摄像头配置
   */
  private cacheToken(key: string, tokenInfo: TokenInfo, cameraConfig: CameraConfig): void {
    this.tokens.set(key, tokenInfo)
    
    // 记录缓存日志
    console.log(`💾 Token已缓存: ${cameraConfig.name}`, {
      expires: new Date(tokenInfo.expireTime).toLocaleString(),
      expiresIn: `${tokenInfo.expiresIn}秒`
    })
  }

  /**
   * 检查Token是否有效
   * @param key 缓存键
   * @returns boolean
   */
  private isTokenValid(key: string): boolean {
    const tokenInfo = this.tokens.get(key)
    if (!tokenInfo) return false
    
    // 提前5分钟刷新Token
    return Date.now() < (tokenInfo.expireTime - this.refreshThreshold)
  }

  /**
   * 安排Token自动刷新
   * @param key 缓存键
   * @param cameraConfig 摄像头配置
   */
  private scheduleTokenRefresh(key: string, cameraConfig: CameraConfig): void {
    const tokenInfo = this.tokens.get(key)
    if (!tokenInfo) return

    // 清除已有的定时器
    if (this.refreshTimers.has(key)) {
      clearTimeout(this.refreshTimers.get(key))
    }

    // 计算刷新时间（提前5分钟）
    const refreshTime = tokenInfo.expireTime - Date.now() - this.refreshThreshold
    
    if (refreshTime > 0) {
      const timer = setTimeout(async () => {
        try {
          console.log(`🔄 自动刷新Token: ${cameraConfig.name}`)
          const newToken = await this.fetchTokenFromDevice(cameraConfig)
          this.cacheToken(key, newToken, cameraConfig)
          this.scheduleTokenRefresh(key, cameraConfig)
        } catch (error) {
          console.error(`❌ 自动刷新Token失败: ${cameraConfig.name}`, error)
          // 刷新失败时，可以尝试重新安排刷新
          setTimeout(() => {
            this.scheduleTokenRefresh(key, cameraConfig)
          }, 60000) // 1分钟后重试
        }
      }, refreshTime)

      this.refreshTimers.set(key, timer)
      
      console.log(`⏰ 已安排Token自动刷新: ${cameraConfig.name} (${Math.round(refreshTime / 1000)}秒后)`)
    }
  }

  /**
   * 手动刷新Token
   * @param cameraConfig 摄像头配置
   * @returns Promise<string>
   */
  async refreshToken(cameraConfig: CameraConfig): Promise<string> {
    const cacheKey = this.getCacheKey(cameraConfig)
    
    try {
      console.log(`🔄 手动刷新Token: ${cameraConfig.name}`)
      const tokenInfo = await this.fetchTokenFromDevice(cameraConfig)
      this.cacheToken(cacheKey, tokenInfo, cameraConfig)
      this.scheduleTokenRefresh(cacheKey, cameraConfig)
      return tokenInfo.token
    } catch (error) {
      console.error(`❌ 手动刷新Token失败: ${cameraConfig.name}`, error)
      throw error
    }
  }

  /**
   * 批量获取多个摄像头Token
   * @param cameras 摄像头配置数组
   * @returns Promise<Array<{success: boolean, cameraId: number, token?: string, error?: string}>>
   */
  async getBatchTokens(cameras: CameraConfig[]) {
    const results = await Promise.allSettled(
      cameras.map(async (camera) => {
        try {
          const token = await this.getCameraToken(camera)
          return {
            success: true,
            cameraId: camera.id,
            cameraName: camera.name,
            token: token
          }
        } catch (error) {
          return {
            success: false,
            cameraId: camera.id,
            cameraName: camera.name,
            error: error instanceof Error ? error.message : '未知错误'
          }
        }
      })
    )

    return results.map(result => 
      result.status === 'fulfilled' ? result.value : {
        success: false,
        error: '请求被拒绝'
      }
    )
  }

  /**
   * 清理特定摄像头的Token缓存
   * @param cameraConfig 摄像头配置
   */
  clearToken(cameraConfig: CameraConfig): void {
    const cacheKey = this.getCacheKey(cameraConfig)
    
    // 清除Token缓存
    this.tokens.delete(cacheKey)
    
    // 清除定时器
    const timer = this.refreshTimers.get(cacheKey)
    if (timer) {
      clearTimeout(timer)
      this.refreshTimers.delete(cacheKey)
    }
    
    console.log(`🗑️ 已清理Token缓存: ${cameraConfig.name}`)
  }

  /**
   * 获取缓存键
   * @param cameraConfig 摄像头配置
   * @returns string
   */
  private getCacheKey(cameraConfig: CameraConfig): string {
    return `${cameraConfig.ip}_${cameraConfig.id}`
  }

  /**
   * 设置清理处理程序
   */
  private setupCleanupHandler(): void {
    // 页面卸载时清理所有定时器
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.destroy()
      })
    }
  }

  /**
   * 获取Token状态信息
   * @param cameraConfig 摄像头配置
   * @returns TokenInfo | null
   */
  getTokenInfo(cameraConfig: CameraConfig): TokenInfo | null {
    const cacheKey = this.getCacheKey(cameraConfig)
    return this.tokens.get(cacheKey) || null
  }

  /**
   * 获取所有缓存的Token状态
   * @returns Map<string, TokenInfo & {cameraKey: string}>
   */
  getAllTokens(): Map<string, TokenInfo & {cameraKey: string}> {
    const result = new Map()
    this.tokens.forEach((tokenInfo, key) => {
      result.set(key, {
        ...tokenInfo,
        cameraKey: key,
        remainingTime: tokenInfo.expireTime - Date.now()
      })
    })
    return result
  }

  /**
   * 销毁管理器，清理所有资源
   */
  destroy(): void {
    console.log('🧹 清理Token管理器资源...')
    
    // 清除所有定时器
    this.refreshTimers.forEach(timer => clearTimeout(timer))
    this.refreshTimers.clear()
    
    // 清除所有Token缓存
    this.tokens.clear()
    
    console.log('✅ Token管理器资源清理完成')
  }
}

// 创建全局单例实例
export const tokenManager = new CameraTokenManager()

export default tokenManager