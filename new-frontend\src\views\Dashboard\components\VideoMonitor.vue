<template>
  <el-card class="video-monitor" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><VideoCamera /></el-icon>
          <span class="header-title">实时监控</span>
          <!-- 摄像头选择器 -->
          <el-select 
            v-model="currentCameraIndex" 
            placeholder="选择摄像头" 
            size="small"
            style="margin-left: 12px; width: 120px;"
            @change="switchCamera"
          >
            <el-option
              v-for="(camera, index) in cameras"
              :key="index"
              :label="camera.name"
              :value="index"
            />
          </el-select>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-tooltip :content="playStatus[currentCameraIndex] ? '停止播放' : '开始播放'" placement="top">
              <el-button 
                size="small"
                @click="toggleVideo"
                :loading="videoLoading"
                :type="playStatus[currentCameraIndex] ? 'danger' : 'primary'"
              >
                <el-icon>
                  <VideoPause v-if="playStatus[currentCameraIndex]" />
                  <VideoPlay v-else />
                </el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="全屏播放" placement="top">
              <el-button 
                size="small"
                @click="enterFullscreen"
                :disabled="!playStatus[currentCameraIndex]"
              >
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="刷新视频" placement="top">
              <el-button 
                size="small"
                @click="refreshVideo"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="手动抓拍" placement="top">
              <el-button 
                size="small"
                @click="capturePhoto"
                :loading="captureLoading"
                :disabled="!playStatus[currentCameraIndex]"
              >
                <el-icon><Camera /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </div>
      </div>
    </template>

    <div class="video-container" ref="videoContainer">
      <!-- 多摄像头视频播放区域 -->
      <div 
        v-for="(camera, index) in cameras" 
        :key="index"
        :class="['video-player', { active: index === currentCameraIndex }]"
        v-show="index === currentCameraIndex"
      >
        <video
          :ref="`videoElement${index}`"
          :id="`video${index}`"
          class="video-element"
          width="100%" 
          height="100%"
          @loadstart="() => handleVideoLoadStart(index)"
          @loadeddata="() => handleVideoLoaded(index)"
          @error="(event) => handleVideoError(event, index)"
        />
        
        <!-- 录制指示器 -->
        <div class="recording-indicator" v-if="playStatus[index]">
          <div class="rec-dot"></div>
          <span class="rec-text">{{ camera.name }}录制中</span>
        </div>

        <!-- 视频控制层 -->
        <div class="video-controls">
          <el-button 
            v-if="!playStatus[index]"
            @click="startVideo(index)"
            type="primary" 
            size="small"
            circle
          >
            <el-icon><VideoPlay /></el-icon>
          </el-button>
          
          <el-button 
            v-if="playStatus[index]"
            @click="stopVideo(index)"
            type="danger" 
            size="small"
            circle
          >
            <el-icon><VideoPause /></el-icon>
          </el-button>
          
          <el-button 
            @click="fullScreen(index)"
            type="info" 
            size="small"
            circle
          >
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 占位符 -->
      <div class="video-placeholder" v-show="!playStatus[currentCameraIndex]">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
          <p class="placeholder-text">{{ cameras[currentCameraIndex]?.name || '摄像头' }}未连接</p>
          <p class="placeholder-desc">点击播放按钮连接视频流</p>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="video-loading" v-if="videoLoading">
        <el-icon class="loading-icon is-loading"><Loading /></el-icon>
        <p>正在连接{{ cameras[currentCameraIndex]?.name }}视频流...</p>
      </div>

      <!-- 连接错误提示 -->
      <div class="video-error" v-if="connectionError">
        <el-icon class="error-icon"><Warning /></el-icon>
        <p>{{ connectionError }}</p>
        <el-button size="small" @click="retryConnection">重试连接</el-button>
      </div>
    </div>

    <!-- 视频信息栏 -->
    <div class="video-info">
      <div class="info-item">
        <span class="info-label">摄像头:</span>
        <span class="info-value">{{ cameras[currentCameraIndex]?.name }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">分辨率:</span>
        <span class="info-value">{{ videoInfo.resolution }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">帧率:</span>
        <span class="info-value">{{ videoInfo.fps }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">状态:</span>
        <el-tag :type="videoStatusType" size="small">{{ videoStatus }}</el-tag>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 基于FLV.js的实时视频监控组件
// 作者：仕伟

import flvjs from 'flv.js'
import axios from 'axios'
import { deviceApi } from '@/services/api'
import { useFullscreen } from '@vueuse/core'
import { VideoPlay, VideoPause, FullScreen, VideoCamera, Loading, Warning, Refresh, Camera } from '@element-plus/icons-vue'
import { 
  videoConfig, 
  getStreamUrl, 
  getCamerasFromStorage, 
  saveCamerasToStorage, 
  type CameraConfig,
  type VideoConfig as VideoConfigType,
  type FlvConfig
} from '@/config/video'
import { tokenManager } from '@/utils/tokenManager'

// 接口定义
interface VideoInfo {
  resolution: string
  fps: string
  bitrate: string
}

// 摄像头配置（从配置文件和本地存储加载）
const cameras = ref<CameraConfig[]>(getCamerasFromStorage())

// 当前选中的摄像头索引
const currentCameraIndex = ref(0)

// FLV播放器实例
const flvPlayers = ref<any[]>([])

// 播放状态数组
const playStatus = ref<boolean[]>([])

// 访问Token数组
const tokens = ref<string[]>([])

// Token过期时间数组
const tokenExpires = ref<number[]>([])

// 组件状态
const videoLoading = ref(false)
const captureLoading = ref(false)
const connectionError = ref('')

// DOM 引用
const videoContainer = ref<HTMLElement>()

// 全屏功能
const { enter: enterFullscreen } = useFullscreen(videoContainer)

// 视频信息
const videoInfo = reactive<VideoInfo>({
  resolution: '1920x1080',
  fps: '25fps',
  bitrate: '2.1Mbps'
})

// 计算属性：视频状态
const videoStatus = computed(() => {
  if (videoLoading.value) return '连接中'
  if (playStatus.value[currentCameraIndex.value]) return '正常'
  if (connectionError.value) return '连接失败'
  return '未连接'
})

const videoStatusType = computed(() => {
  if (videoLoading.value) return 'warning'
  if (playStatus.value[currentCameraIndex.value]) return 'success'
  if (connectionError.value) return 'danger'
  return 'info'
})

// 生命周期：组件挂载
onMounted(() => {
  initializeVideos()
})

// 生命周期：组件销毁时清理资源
onUnmounted(() => {
  destroyAllPlayers()
})

// 初始化视频
const initializeVideos = async () => {
  // 初始化数组
  for (let i = 0; i < cameras.value.length; i++) {
    playStatus.value.push(false)
    flvPlayers.value.push(null)
    tokens.value.push('')
    tokenExpires.value.push(0)
  }
  
  // 保存摄像头配置到本地存储
  saveCamerasToStorage(cameras.value)
}

// 获取视频流Token（使用统一Token管理器）
const getVideoToken = async (index: number): Promise<boolean> => {
  try {
    const camera = cameras.value[index]
    console.log(`🔐 获取Token: ${camera.name}`)
    
    // 使用Token管理器获取Token
    const token = await tokenManager.getCameraToken(camera)
    
    // 更新组件状态
    tokens.value[index] = token
    
    // 获取Token信息用于显示过期时间
    const tokenInfo = tokenManager.getTokenInfo(camera)
    if (tokenInfo) {
      tokenExpires.value[index] = tokenInfo.expireTime
      console.log(`📅 Token过期时间: ${new Date(tokenInfo.expireTime).toLocaleString()}`)
    }
    
    return true
    
  } catch (error) {
    console.error(`❌ ${cameras.value[index].name} Token获取失败:`, error)
    connectionError.value = `${cameras.value[index].name}连接失败：${error instanceof Error ? error.message : '未知错误'}`
    ElMessage.error(`${cameras.value[index].name}连接失败`)
    return false
  }
}

// 创建FLV播放器
const createFlvPlayer = (index: number): boolean => {
  try {
    if (!flvjs.isSupported()) {
      console.error('FLV.js is not supported in this browser.')
      connectionError.value = '当前浏览器不支持视频播放'
      ElMessage.error('当前浏览器不支持视频播放')
      return false
    }
    
    const videoElement = document.getElementById(`video${index}`) as HTMLVideoElement
    if (!videoElement) {
      console.error(`视频元素 video${index} 未找到`)
      connectionError.value = '视频元素初始化失败'
      return false
    }
    
    // 构建WebSocket URL（使用配置文件的函数）
    const streamUrl = getStreamUrl(cameras.value[index], tokens.value[index])
    
    // 创建FLV播放器（使用配置文件的设置）
    const flvPlayer = flvjs.createPlayer({
      type: 'flv',
      url: streamUrl,
      ...videoConfig.flv // 使用配置文件中的FLV设置
    })
    
    // 挂载到DOM元素
    flvPlayer.attachMediaElement(videoElement)
    
    // 设置错误处理
    setupErrorHandling(flvPlayer, index)
    
    // 保存播放器实例
    flvPlayers.value[index] = flvPlayer
    
    console.log(`${cameras.value[index].name} 播放器创建成功`)
    return true
  } catch (error) {
    console.error(`${cameras.value[index].name} 播放器创建失败:`, error)
    connectionError.value = `${cameras.value[index].name}播放器创建失败`
    return false
  }
}

// 设置错误处理
const setupErrorHandling = (flvPlayer: any, index: number) => {
  flvPlayer.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string) => {
    console.error(`${cameras.value[index].name} 播放错误:`, errorType, errorDetail)
    
    if (errorType === flvjs.ErrorTypes.MEDIA_ERROR) {
      console.log('媒体错误')
      if (errorDetail === flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {
        connectionError.value = '视频格式不支持'
        ElMessage.error('视频格式不支持')
      }
    } else if (errorType === flvjs.ErrorTypes.NETWORK_ERROR) {
      console.log('网络错误')
      if (errorDetail === flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {
        console.log('HTTP状态码异常')
        connectionError.value = '网络连接异常，尝试重新连接...'
        // 尝试重新获取Token并连接
        retryConnection(index)
      }
    } else if (errorType === flvjs.ErrorTypes.OTHER_ERROR) {
      console.log('其他异常:', errorDetail)
      connectionError.value = `播放异常: ${errorDetail}`
    }
  })
  
  // 监听统计信息
  flvPlayer.on(flvjs.Events.STATISTICS_INFO, (res: any) => {
    // 更新视频信息
    if (res.videoWidth && res.videoHeight) {
      videoInfo.resolution = `${res.videoWidth}x${res.videoHeight}`
    }
    if (res.fps) {
      videoInfo.fps = `${Math.round(res.fps)}fps`
    }
    if (res.speed) {
      videoInfo.bitrate = `${(res.speed / 1024).toFixed(1)}Mbps`
    }
  })
}

// 切换视频播放状态
const toggleVideo = async () => {
  if (playStatus.value[currentCameraIndex.value]) {
    stopVideo(currentCameraIndex.value)
  } else {
    await startVideo(currentCameraIndex.value)
  }
}

// 开始播放视频
const startVideo = async (index: number): Promise<boolean> => {
  videoLoading.value = true
  connectionError.value = ''
  
  try {
    // 检查摄像头是否启用
    if (!cameras.value[index].enabled) {
      throw new Error('摄像头已被禁用')
    }
    
    // 如果播放器不存在，先获取Token并创建播放器
    if (!flvPlayers.value[index]) {
      const tokenSuccess = await getVideoToken(index)
      if (!tokenSuccess) {
        return false
      }
      
      const playerSuccess = createFlvPlayer(index)
      if (!playerSuccess) {
        return false
      }
    }
    
    const flvPlayer = flvPlayers.value[index]
    const videoElement = document.getElementById(`video${index}`) as HTMLVideoElement
    
    if (!flvPlayer || !videoElement) {
      throw new Error('播放器或视频元素不存在')
    }
    
    flvPlayer.attachMediaElement(videoElement)
    await flvPlayer.load()
    await flvPlayer.play()
    
    playStatus.value[index] = true
    connectionError.value = ''
    ElMessage.success(`${cameras.value[index].name}开始播放`)
    return true
    
  } catch (error) {
    console.error(`${cameras.value[index].name} 播放失败:`, error)
    connectionError.value = `${cameras.value[index].name}播放失败: ${error instanceof Error ? error.message : '未知错误'}`
    ElMessage.error(`${cameras.value[index].name}播放失败`)
    return false
  } finally {
    videoLoading.value = false
  }
}

// 停止播放视频
const stopVideo = (index: number) => {
  if (flvPlayers.value[index]) {
    flvPlayers.value[index].pause()
    flvPlayers.value[index].unload()
    playStatus.value[index] = false
    ElMessage.info(`${cameras.value[index].name}已停止播放`)
  }
}

// 全屏播放
const fullScreen = (index: number) => {
  const videoElement = document.getElementById(`video${index}`) as HTMLVideoElement
  if (videoElement?.requestFullscreen) {
    videoElement.requestFullscreen()
  }
}

// 切换摄像头
const switchCamera = (newIndex: number) => {
  currentCameraIndex.value = newIndex
  connectionError.value = ''
}

// 重试连接（使用配置文件的重试间隔和最大重试次数）
const retryConnection = async (index?: number, retryCount = 0) => {
  const targetIndex = index !== undefined ? index : currentCameraIndex.value
  
  if (retryCount >= videoConfig.video.maxRetries) {
    connectionError.value = `${cameras.value[targetIndex].name}重试${videoConfig.video.maxRetries}次后仍然失败，请检查网络连接`
    ElMessage.error(`${cameras.value[targetIndex].name}连接失败，已达到最大重试次数`)
    return
  }
  
  console.log(`${cameras.value[targetIndex].name} 尝试重新连接... (第${retryCount + 1}次)`)
  
  stopVideo(targetIndex)
  connectionError.value = `${cameras.value[targetIndex].name}重新连接中... (${retryCount + 1}/${videoConfig.video.maxRetries})`
  
  setTimeout(async () => {
    // 清除过期的Token
    tokens.value[targetIndex] = ''
    tokenExpires.value[targetIndex] = 0
    
    const tokenSuccess = await getVideoToken(targetIndex)
    if (tokenSuccess) {
      flvPlayers.value[targetIndex] = null // 重置播放器
      const success = await startVideo(targetIndex)
      if (!success) {
        // 如果启动失败，继续重试
        await retryConnection(targetIndex, retryCount + 1)
      }
    } else {
      // 如果获取Token失败，也要重试
      await retryConnection(targetIndex, retryCount + 1)
    }
  }, videoConfig.video.reconnectInterval)
}

// 刷新所有视频
const refreshVideo = async () => {
  destroyAllPlayers()
  await initializeVideos()
  ElMessage.success('视频已刷新')
}

// 手动抓拍
const capturePhoto = async () => {
  if (!playStatus.value[currentCameraIndex.value]) {
    ElMessage.warning('请先开启视频监控')
    return
  }

  captureLoading.value = true
  
  try {
    // 使用canvas从视频元素中获取当前帧
    const videoElement = document.getElementById(`video${currentCameraIndex.value}`) as HTMLVideoElement
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (videoElement && ctx) {
      canvas.width = videoElement.videoWidth || 1920
      canvas.height = videoElement.videoHeight || 1080
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      
      // 转换为blob并保存
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `capture_${cameras.value[currentCameraIndex.value].name}_${new Date().getTime()}.jpg`
          a.click()
          URL.revokeObjectURL(url)
          ElMessage.success('抓拍成功')
        }
      }, 'image/jpeg', 0.8)
    }
  } catch (error) {
    console.error('抓拍失败:', error)
    ElMessage.error('抓拍失败')
  } finally {
    captureLoading.value = false
  }
}

// 视频事件处理
const handleVideoLoadStart = (index: number) => {
  console.log(`${cameras.value[index].name} 视频开始加载`)
}

const handleVideoLoaded = (index: number) => {
  console.log(`${cameras.value[index].name} 视频加载完成`)
  connectionError.value = ''
}

const handleVideoError = (event: Event, index: number) => {
  console.error(`${cameras.value[index].name} 视频播放错误:`, event)
  connectionError.value = `${cameras.value[index].name}播放出现错误`
  ElMessage.error(`${cameras.value[index].name}播放出现错误`)
  playStatus.value[index] = false
  videoLoading.value = false
}

// 销毁所有播放器
const destroyAllPlayers = () => {
  flvPlayers.value.forEach((player, index) => {
    if (player) {
      player.pause()
      player.unload()
      player.detachMediaElement()
      player.destroy()
      flvPlayers.value[index] = null
    }
  })
  
  // 清理Token缓存
  cameras.value.forEach(camera => {
    tokenManager.clearToken(camera)
  })
  
  playStatus.value = playStatus.value.map(() => false)
  connectionError.value = ''
}
</script>

<style scoped lang="scss">
.video-monitor {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .header-right {
      .el-button-group {
        .el-button {
          border: 1px solid var(--border-base);
        }
      }
    }
  }

  .video-container {
    flex: 1;
    position: relative;
    background: #000;
    border-radius: var(--border-radius-base);
    overflow: hidden;
    min-height: 350px;
    
    /* 电脑端响应式调整 */
    @media (min-width: 1600px) {
      min-height: 450px; /* 超大屏更高的视频区域 */
    }
    
    @media (min-width: 1200px) and (max-width: 1599px) {
      min-height: 400px; /* 大屏适中的视频区域 */
    }
    
    @media (max-width: 1199px) {
      min-height: 320px; /* 中小屏紧凑的视频区域 */
    }

    .video-player {
      width: 100%;
      height: 100%;
      position: relative;
      
      &.active {
        z-index: 1;
      }

      .video-element {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: #000;
        
        // 隐藏默认视频控件
        &::-webkit-media-controls {
          display: none;
        }
        
        &::-webkit-media-controls-enclosure {
          display: none;
        }
      }

      .recording-indicator {
        position: absolute;
        top: var(--spacing-md);
        left: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: rgba(0, 0, 0, 0.8);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-base);
        color: #fff;
        font-size: 12px;
        z-index: 10;

        .rec-dot {
          width: 8px;
          height: 8px;
          background: #ff4d4f;
          border-radius: 50%;
          animation: pulse 1.5s infinite;
        }

        .rec-text {
          font-weight: 500;
        }
      }

      // 视频控制按钮层
      .video-controls {
        position: absolute;
        bottom: var(--spacing-md);
        left: var(--spacing-md);
        z-index: 10;
        display: flex;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: opacity 0.3s ease;
        
        .el-button {
          background: rgba(0, 0, 0, 0.7);
          border: none;
          color: #fff;
          
          &:hover {
            background: rgba(0, 0, 0, 0.9);
          }
        }
      }
      
      // 鼠标悬停时显示控制按钮
      &:hover .video-controls {
        opacity: 1;
      }
    }

    .video-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);

      .placeholder-content {
        text-align: center;
        color: var(--text-secondary);

        .placeholder-icon {
          font-size: 48px;
          color: var(--text-placeholder);
          margin-bottom: var(--spacing-md);
        }

        .placeholder-text {
          font-size: 16px;
          margin: 0 0 var(--spacing-xs) 0;
        }

        .placeholder-desc {
          font-size: 14px;
          margin: 0;
          opacity: 0.7;
        }
      }
    }

    .video-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.9);
      color: #fff;
      z-index: 20;

      .loading-icon {
        font-size: 32px;
        margin-bottom: var(--spacing-md);
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
    
    // 连接错误提示
    .video-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.9);
      color: #fff;
      z-index: 20;
      text-align: center;
      padding: var(--spacing-lg);

      .error-icon {
        font-size: 48px;
        color: #ff4d4f;
        margin-bottom: var(--spacing-md);
      }

      p {
        margin: 0 0 var(--spacing-lg) 0;
        font-size: 14px;
        line-height: 1.5;
      }
      
      .el-button {
        background: var(--el-color-primary);
        border: none;
        color: #fff;
      }
    }
  }

  .video-info {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-base);
    flex-wrap: wrap;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      justify-content: flex-start;
      gap: var(--spacing-xs);
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: 12px;
      min-width: 120px;

      @media (max-width: 768px) {
        min-width: auto;
        flex: 1;
      }

      .info-label {
        color: var(--text-secondary);
        white-space: nowrap;
      }

      .info-value {
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

// 电脑端响应式优化
@media (min-width: 1600px) {
  .video-monitor {
    .card-header {
      .header-left {
        .header-title {
          font-size: 18px;
        }
        
        .el-select {
          width: 140px !important;
        }
      }
      
      .header-right {
        .el-button {
          padding: 8px 12px;
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
    
    .video-info {
      padding: var(--spacing-md) var(--spacing-lg);
      
      .info-item {
        font-size: 13px;
        min-width: 140px;
      }
    }
  }
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .video-monitor {
    .card-header {
      .header-left {
        .el-select {
          width: 130px !important;
        }
      }
    }
  }
}

@media (max-width: 1199px) {
  .video-monitor {
    .card-header {
      .header-left {
        .header-title {
          font-size: 14px;
        }
        
        .el-select {
          width: 110px !important;
        }
      }
      
      .header-right {
        .el-button {
          padding: 6px 8px;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
    
    .video-info {
      .info-item {
        font-size: 11px;
        min-width: 100px;
      }
    }
  }
}

// 移动端响应式布局
@media (max-width: 768px) {
  .video-monitor {
    .card-header {
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: flex-start;
      
      .header-left {
        width: 100%;
        justify-content: space-between;
        
        .el-select {
          margin-left: 0 !important;
        }
      }
      
      .header-right {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
    
    .video-container {
      min-height: 250px;
    }
  }
}
</style>