// 主题颜色变量
// 作者：仕伟

// 主色调
:root {
  // Element Plus 主色调
  --el-color-primary: #409EFF;
  --el-color-success: #67C23A;
  --el-color-warning: #E6A23C;
  --el-color-danger: #F56C6C;
  --el-color-info: #909399;

  // 自定义工业控制色彩
  --color-device-online: #52C41A;
  --color-device-offline: #FF4D4F;
  --color-device-warning: #FAAD14;
  --color-weight-normal: #1890FF;
  --color-weight-overload: #FF7A45;
  --color-plate-valid: #52C41A;
  --color-plate-invalid: #FF4D4F;

  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f5f7fa;
  --bg-tertiary: #ebeef5;
  --bg-card: #ffffff;
  --bg-sidebar: #304156;
  --bg-header: #ffffff;

  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  --text-inverse: #ffffff;

  // 边框颜色
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;

  // 阴影
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

  // 圆角
  --border-radius-small: 2px;
  --border-radius-base: 4px;
  --border-radius-large: 6px;
  --border-radius-round: 20px;
  --border-radius-circle: 100%;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  // 过渡动画
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fade: opacity 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  --transition-bounce: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

// 暗色主题
[data-theme='dark'] {
  // 背景色
  --bg-primary: #141414;
  --bg-secondary: #1f1f1f;
  --bg-tertiary: #262626;
  --bg-card: #1f1f1f;
  --bg-sidebar: #001529;
  --bg-header: #1f1f1f;

  // 文本颜色
  --text-primary: #ffffff;
  --text-regular: #d9d9d9;
  --text-secondary: #8c8c8c;
  --text-placeholder: #595959;
  --text-inverse: #000000;

  // 边框颜色
  --border-base: #303030;
  --border-light: #404040;
  --border-lighter: #262626;
  --border-extra-light: #1f1f1f;

  // 阴影 (暗色主题下更深的阴影)
  --shadow-base: 0 2px 8px rgba(0, 0, 0, 0.45);
  --shadow-light: 0 4px 16px 0 rgba(0, 0, 0, 0.25);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.35);

  // 设备状态色在暗色主题下的调整
  --color-device-online: #73d13d;
  --color-device-offline: #ff7875;
  --color-device-warning: #ffc069;
}

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1600px;
$breakpoint-xxxl: 1920px;

// 电脑端专用断点
$desktop-sm: 1366px;  // 小型笔记本
$desktop-md: 1440px;  // 中型显示器
$desktop-lg: 1680px;  // 大型显示器
$desktop-xl: 1920px;  // 全高清
$desktop-xxl: 2560px; // 2K显示器