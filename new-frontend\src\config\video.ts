// 视频流监控系统配置文件
// 作者：仕伟

/**
 * 摄像头配置接口
 */
export interface CameraConfig {
  id: number
  ip: string
  port: number
  channel: number
  name: string
  enabled: boolean
  description?: string
}

/**
 * API配置接口
 */
export interface ApiConfig {
  baseURL: string
  endpoints: {
    getToken: string
    deviceStatus: string
    capturePhoto: string
  }
  timeout: number
}

/**
 * FLV播放器配置接口
 */
export interface FlvConfig {
  enableWorker: boolean
  enableStashBuffer: boolean
  stashInitialSize?: number
  isLive: boolean
  lazyLoad: boolean
  lazyLoadMaxDuration: number
  deferLoadAfterSourceOpen: boolean
  statisticsInfoReportInterval: number
  hasVideo: boolean
  hasAudio: boolean
}

/**
 * 视频配置接口
 */
export interface VideoConfig {
  defaultPort: number
  reconnectInterval: number
  maxRetries: number
  tokenExpireDuration: number
  supportedFormats: string[]
}

/**
 * 视频流监控系统配置
 */
export const videoConfig = {
  // Token获取模式配置
  tokenMode: 'direct' as 'direct' | 'proxy' | 'auth_center', // direct: 直接向设备请求（旧项目方案）
  
  // API配置
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8077',
    endpoints: {
      getToken: '/car/getClient',        // 后端代理方式的接口
      getProjectId: '/car/getProjectId', // 获取摄像头配置的接口
      deviceStatus: '/device/status',
      capturePhoto: '/device/capture'
    },
    timeout: 30000
  } as ApiConfig,
  
  // 设备直接访问配置（旧项目方案）
  device: {
    tokenEndpoint: '/request.php',        // 设备Token接口路径
    requestTimeout: 8000,                 // 请求超时时间
    enableCORS: true,                     // 是否启用跨域支持
    retryOnFailure: true,                 // 失败时是否重试
    protocol: 'http'                      // 设备通信协议
  },
  
  // 视频配置
  video: {
    defaultPort: 9080,
    reconnectInterval: 3000,
    maxRetries: 3,
    tokenExpireDuration: 3600000, // 1小时
    supportedFormats: ['flv', 'mp4', 'hls']
  } as VideoConfig,
  
  // FLV播放器配置
  flv: {
    enableWorker: true,
    enableStashBuffer: false,
    stashInitialSize: undefined,
    isLive: true,
    lazyLoad: true,
    lazyLoadMaxDuration: 3 * 60, // 3分钟
    deferLoadAfterSourceOpen: true,
    statisticsInfoReportInterval: 600,
    hasVideo: true,
    hasAudio: false
  } as FlvConfig,
  
  // 默认摄像头配置
  defaultCameras: [
    {
      id: 1,
      ip: '*************',
      port: 9080,
      channel: 0,
      name: '主通道摄像头',
      enabled: true,
      description: '监控主通道车辆进出'
    },
    {
      id: 2,
      ip: '*************', 
      port: 9080,
      channel: 0,
      name: '称重区摄像头',
      enabled: true,
      description: '监控称重区域情况'
    }
  ] as CameraConfig[]
}

/**
 * 获取视频流URL
 * @param camera 摄像头配置
 * @param token 访问令牌
 * @returns WebSocket视频流URL
 */
export const getStreamUrl = (camera: CameraConfig, token: string): string => {
  return `ws://${camera.ip}:${camera.port}/ws.flv?token=${token}&channel=${camera.channel}`
}

/**
 * 获取RTMP推流URL
 * @param camera 摄像头配置
 * @param token 访问令牌
 * @returns RTMP推流URL
 */
export const getRtmpUrl = (camera: CameraConfig, token: string): string => {
  return `rtmp://${camera.ip}:1935/live/camera${camera.id}?token=${token}`
}

/**
 * 获取HLS播放URL
 * @param camera 摄像头配置
 * @param token 访问令牌
 * @returns HLS播放URL
 */
export const getHlsUrl = (camera: CameraConfig, token: string): string => {
  return `http://${camera.ip}:8080/live/camera${camera.id}.m3u8?token=${token}`
}

/**
 * 验证摄像头配置
 * @param camera 摄像头配置
 * @returns 验证结果
 */
export const validateCameraConfig = (camera: CameraConfig): boolean => {
  // 验证IP地址格式
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (!ipRegex.test(camera.ip)) {
    return false
  }
  
  // 验证端口范围
  if (camera.port < 1 || camera.port > 65535) {
    return false
  }
  
  // 验证通道号
  if (camera.channel < 0) {
    return false
  }
  
  // 验证ID
  if (camera.id <= 0) {
    return false
  }
  
  return true
}

/**
 * 从本地存储获取摄像头配置
 * @returns 摄像头配置数组
 */
export const getCamerasFromStorage = (): CameraConfig[] => {
  try {
    const stored = localStorage.getItem('video_cameras_config')
    if (stored) {
      const cameras = JSON.parse(stored) as CameraConfig[]
      return cameras.filter(camera => validateCameraConfig(camera))
    }
  } catch (error) {
    console.warn('获取本地摄像头配置失败:', error)
  }
  
  return videoConfig.defaultCameras
}

/**
 * 保存摄像头配置到本地存储
 * @param cameras 摄像头配置数组
 */
export const saveCamerasToStorage = (cameras: CameraConfig[]): void => {
  try {
    const validCameras = cameras.filter(camera => validateCameraConfig(camera))
    localStorage.setItem('video_cameras_config', JSON.stringify(validCameras))
  } catch (error) {
    console.error('保存摄像头配置失败:', error)
  }
}

/**
 * 获取视频质量配置
 */
export const getVideoQualityOptions = () => [
  { label: '超清 (1920x1080)', value: '1080p', width: 1920, height: 1080 },
  { label: '高清 (1280x720)', value: '720p', width: 1280, height: 720 },
  { label: '标清 (854x480)', value: '480p', width: 854, height: 480 },
  { label: '流畅 (640x360)', value: '360p', width: 640, height: 360 }
]

/**
 * 获取视频编码格式选项
 */
export const getVideoCodecOptions = () => [
  { label: 'H.264', value: 'h264', description: '兼容性好，广泛支持' },
  { label: 'H.265', value: 'h265', description: '压缩率高，占用带宽少' },
  { label: 'VP8', value: 'vp8', description: 'WebRTC标准编码' },
  { label: 'VP9', value: 'vp9', description: '新一代开源编码' }
]

export default videoConfig