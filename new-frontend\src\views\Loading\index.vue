<template>
  <div class="loading-page">
    <div class="loading-content">
      <!-- Logo 和标题 -->
      <div class="logo-section">
        <img src="/logo.svg" alt="系统Logo" class="logo" />
        <h1 class="title">河北省采(弃)砂监管一体机系统</h1>
        <p class="version">v2.0.0</p>
      </div>

      <!-- 加载动画 -->
      <div class="loading-animation">
        <div class="spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>

      <!-- 加载进度 -->
      <div class="loading-progress">
        <el-progress 
          :percentage="loadingProgress" 
          :show-text="false"
          :stroke-width="4"
          color="#409EFF"
        />
        <p class="progress-text">系统初始化中... {{ loadingProgress }}%</p>
      </div>

      <!-- 系统信息 -->
      <div class="system-info">
        <div class="info-item">
          <span class="label">作者:</span>
          <span class="value">仕伟</span>
        </div>
        <div class="info-item">
          <span class="label">版本:</span>
          <span class="value">2.0.0</span>
        </div>
        <div class="info-item">
          <span class="label">构建时间:</span>
          <span class="value">{{ buildTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 系统启动加载页面
// 作者：仕伟

import { vehicleApi } from '@/services/api'
import dayjs from 'dayjs'

const router = useRouter()

// 加载状态
const loadingProgress = ref(0)
const loadingText = ref('正在启动系统中，请耐心等待...')

// 构建时间
const buildTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

// 加载步骤
const loadingSteps = [
  { text: '初始化系统配置...', duration: 1000 },
  { text: '连接后端服务...', duration: 1500 },
  { text: '检查设备状态...', duration: 1000 },
  { text: '加载用户界面...', duration: 800 },
  { text: '系统准备就绪...', duration: 500 }
]

// 执行加载流程
const startLoading = async () => {
  let currentProgress = 0
  const stepProgress = 100 / loadingSteps.length

  for (let i = 0; i < loadingSteps.length; i++) {
    const step = loadingSteps[i]
    loadingText.value = step.text

    // 模拟加载过程
    await new Promise(resolve => {
      const interval = setInterval(() => {
        currentProgress += stepProgress / 10
        loadingProgress.value = Math.min(currentProgress, (i + 1) * stepProgress)
        
        if (loadingProgress.value >= (i + 1) * stepProgress) {
          clearInterval(interval)
          resolve(void 0)
        }
      }, step.duration / 10)
    })

    // 在第二步时尝试连接后端
    if (i === 1) {
      try {
        await vehicleApi.getProjectConfig()
        console.log('后端连接成功')
      } catch (error) {
        console.warn('后端连接失败，使用模拟数据', error)
      }
    }
  }

  // 完成加载，跳转到主页面
  loadingProgress.value = 100
  loadingText.value = '系统启动完成'
  
  setTimeout(() => {
    router.replace('/dashboard')
  }, 500)
}

// 页面加载时开始加载流程
onMounted(() => {
  startLoading()
})
</script>

<style scoped lang="scss">
.loading-page {
  height: 100vh;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    animation: float 20s ease-in-out infinite;
    opacity: 0.1;
  }

  .loading-content {
    text-align: center;
    color: white;
    z-index: 1;
    max-width: 400px;
    padding: var(--spacing-xl);

    .logo-section {
      margin-bottom: var(--spacing-xxl);

      .logo {
        width: 80px;
        height: 80px;
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius-large);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      }

      .title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 var(--spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.3;
      }

      .version {
        font-size: 14px;
        margin: 0;
        opacity: 0.8;
        font-weight: 300;
      }
    }

    .loading-animation {
      margin-bottom: var(--spacing-xxl);

      .spinner {
        position: relative;
        width: 80px;
        height: 80px;
        margin: 0 auto var(--spacing-lg) auto;

        .spinner-ring {
          position: absolute;
          width: 100%;
          height: 100%;
          border: 2px solid transparent;
          border-top: 2px solid rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          animation: spin 1.5s linear infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: -0.5s;
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
          }

          &:nth-child(3) {
            animation-delay: -1s;
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
          }
        }
      }

      .loading-text {
        font-size: 16px;
        margin: 0;
        font-weight: 400;
        opacity: 0.9;
        min-height: 24px;
      }
    }

    .loading-progress {
      margin-bottom: var(--spacing-xl);

      :deep(.el-progress-bar__outer) {
        background-color: rgba(255, 255, 255, 0.2);
      }

      :deep(.el-progress-bar__inner) {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
      }

      .progress-text {
        margin: var(--spacing-md) 0 0 0;
        font-size: 14px;
        opacity: 0.8;
      }
    }

    .system-info {
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-xs) 0;
        font-size: 12px;
        opacity: 0.7;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-weight: 300;
        }

        .value {
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 移动端适配
@media (max-width: $breakpoint-sm) {
  .loading-page {
    .loading-content {
      padding: var(--spacing-lg);
      max-width: 320px;

      .logo-section {
        .title {
          font-size: 20px;
        }

        .logo {
          width: 60px;
          height: 60px;
        }
      }

      .loading-animation {
        .spinner {
          width: 60px;
          height: 60px;

          .spinner-ring {
            &:nth-child(2) {
              width: 45px;
              height: 45px;
              top: 7.5px;
              left: 7.5px;
            }

            &:nth-child(3) {
              width: 30px;
              height: 30px;
              top: 15px;
              left: 15px;
            }
          }
        }

        .loading-text {
          font-size: 14px;
        }
      }
    }
  }
}
</style>