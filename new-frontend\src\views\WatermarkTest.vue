<template>
  <div class="watermark-test-page">
    <h1>水印测试页面</h1>
    <div class="test-content">
      <p><strong>项目名称：</strong>兴隆县清水河主要支流水生态环境治理工程</p>
      <p>这是用来测试水印显示效果的页面，背景为白色以检验灰色水印的可见性</p>
      <div class="controls">
        <el-button type="primary" @click="toggleWatermark">
          {{ watermarkStore.isVisible ? '隐藏水印' : '显示水印' }}
        </el-button>
        <el-button @click="toggleMode">
          切换到{{ useFullscreen ? '角落' : '全屏' }}水印
        </el-button>
      </div>
      <div class="status">
        <p><strong>当前模式:</strong> {{ useFullscreen ? '全屏水印' : '角落水印' }}</p>
        <p><strong>水印状态:</strong> {{ watermarkStore.isVisible ? '显示中' : '已隐藏' }}</p>
        <p><strong>透明度:</strong> {{ Math.round(watermarkStore.opacity * 100) }}%</p>
      </div>
    </div>

    <!-- 角落水印 -->
    <Watermark 
      :text="watermarkStore.watermarkText"
      :visible="watermarkStore.isVisible && !useFullscreen"
      :position="watermarkStore.position"
      :opacity="0.5"
      :rotation="watermarkStore.rotation"
      :fontSize="18"
      color="#ff0000"
    />
    
    <!-- 全屏水印 -->
    <FullScreenWatermark 
      :text="watermarkStore.watermarkText"
      :visible="watermarkStore.isVisible && useFullscreen"
      :opacity="0.1"
      :rotation="watermarkStore.rotation"
      :fontSize="16"
      :spacing="200"
      color="#0066cc"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useWatermarkStore } from '@/stores/watermark'
import Watermark from '@/components/Watermark.vue'
import FullScreenWatermark from '@/components/FullScreenWatermark.vue'

const watermarkStore = useWatermarkStore()
const useFullscreen = ref(true)

const toggleWatermark = () => {
  watermarkStore.toggleVisibility()
}

const toggleMode = () => {
  useFullscreen.value = !useFullscreen.value
}
</script>

<style scoped lang="scss">
.watermark-test-page {
  padding: 40px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 40px;
  }
  
  .test-content {
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 12px;
    
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
}
</style>