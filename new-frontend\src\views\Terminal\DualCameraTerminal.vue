<template>
  <div class="terminal-system" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 系统状态栏 -->
    <div class="system-statusbar">
      <div class="status-left">
        <div class="system-logo">
          <img src="/logo.svg" alt="系统Logo" />
          <span class="system-title">河北省采(弃)砂监管一体机</span>
        </div>
        <div class="device-status">
          <StatusIndicator 
            v-for="device in deviceList"
            :key="device.name"
            :status="device.status"
            :label="device.label"
            :icon="device.icon"
            size="small"
          />
        </div>
      </div>
      
      <div class="status-right">
        <div class="system-info">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date">{{ currentDate }}</div>
        </div>
        <div class="system-controls">
          <ThemeToggle />
          <el-button 
            icon="Setting" 
            circle 
            size="small"
            @click="showSettings = true"
          />
        </div>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="terminal-workspace">
      <!-- 左侧双摄像头监控区域 -->
      <div class="left-panel">
        <!-- 双摄像头监控 -->
        <div class="dual-monitor">
          <div class="monitor-header">
            <h3>双摄像头监控</h3>
            <div class="monitor-controls">
              <el-button size="small" @click="toggleAllCameras">
                {{ allCamerasPlaying ? '停止全部' : '开始全部' }}
              </el-button>
              <el-button size="small" @click="captureAllPhotos">
                <el-icon><Camera /></el-icon>
                全部抓拍
              </el-button>
            </div>
          </div>
          
          <!-- 双摄像头画面 -->
          <div class="cameras-grid">
            <!-- 进场摄像头 -->
            <div class="camera-item">
              <div class="camera-header">
                <span class="camera-label">进场监控</span>
                <div class="camera-status" :class="{ online: cameras.entrance.online }">
                  <div class="status-dot"></div>
                  {{ cameras.entrance.online ? '在线' : '离线' }}
                </div>
              </div>
              <div class="video-container">
                <div class="video-player" :class="{ playing: cameras.entrance.playing }">
                  <div v-if="!cameras.entrance.playing" class="video-placeholder">
                    <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
                    <p>进场监控</p>
                    <el-button size="small" type="primary" @click="toggleCamera('entrance')">
                      开始监控
                    </el-button>
                  </div>
                  <div v-else class="video-stream">
                    <video 
                      class="video-element" 
                      autoplay 
                      muted
                      :poster="cameras.entrance.poster"
                    ></video>
                    <div class="video-overlay">
                      <div class="recording-indicator">
                        <div class="rec-dot"></div>
                        <span>REC</span>
                      </div>
                      <div class="video-info">
                        <div>{{ cameras.entrance.resolution }}</div>
                        <div>{{ cameras.entrance.fps }}fps</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="camera-controls">
                  <el-button size="small" @click="toggleCamera('entrance')">
                    {{ cameras.entrance.playing ? '停止' : '播放' }}
                  </el-button>
                  <el-button size="small" @click="capturePhoto('entrance')">
                    <el-icon><Camera /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 出场摄像头 -->
            <div class="camera-item">
              <div class="camera-header">
                <span class="camera-label">出场监控</span>
                <div class="camera-status" :class="{ online: cameras.exit.online }">
                  <div class="status-dot"></div>
                  {{ cameras.exit.online ? '在线' : '离线' }}
                </div>
              </div>
              <div class="video-container">
                <div class="video-player" :class="{ playing: cameras.exit.playing }">
                  <div v-if="!cameras.exit.playing" class="video-placeholder">
                    <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
                    <p>出场监控</p>
                    <el-button size="small" type="primary" @click="toggleCamera('exit')">
                      开始监控
                    </el-button>
                  </div>
                  <div v-else class="video-stream">
                    <video 
                      class="video-element" 
                      autoplay 
                      muted
                      :poster="cameras.exit.poster"
                    ></video>
                    <div class="video-overlay">
                      <div class="recording-indicator">
                        <div class="rec-dot"></div>
                        <span>REC</span>
                      </div>
                      <div class="video-info">
                        <div>{{ cameras.exit.resolution }}</div>
                        <div>{{ cameras.exit.fps }}fps</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="camera-controls">
                  <el-button size="small" @click="toggleCamera('exit')">
                    {{ cameras.exit.playing ? '停止' : '播放' }}
                  </el-button>
                  <el-button size="small" @click="capturePhoto('exit')">
                    <el-icon><Camera /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 当前抓拍图片 -->
        <div class="current-captures">
          <div class="captures-header">
            <h4>当前抓拍</h4>
            <div class="captures-actions">
              <el-button size="small" @click="clearAllPhotos">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
          <div class="captures-grid">
            <!-- 进场抓拍 -->
            <div class="capture-item">
              <div class="capture-header">
                <span class="capture-label">进场抓拍</span>
                <el-button 
                  size="small" 
                  @click="capturePhoto('entrance')"
                  :disabled="!cameras.entrance.playing"
                >
                  <el-icon><Camera /></el-icon>
                  抓拍
                </el-button>
              </div>
              <div class="capture-content">
                <div v-if="currentPhotos.entrance" class="photo-display">
                  <img :src="currentPhotos.entrance.thumbnail" alt="进场抓拍" />
                  <div class="photo-overlay">
                    <div class="photo-time">{{ formatPhotoTime(currentPhotos.entrance.timestamp) }}</div>
                    <div v-if="currentPhotos.entrance.plateNumber" class="photo-plate">
                      {{ currentPhotos.entrance.plateNumber }}
                    </div>
                  </div>
                  <el-button 
                    class="photo-delete" 
                    size="small" 
                    type="danger" 
                    @click.stop="deletePhoto('entrance')"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div v-else class="no-capture">
                  <el-icon><Picture /></el-icon>
                  <p>点击抓拍按钮<br>进行抓拍</p>
                </div>
              </div>
            </div>

            <!-- 出场抓拍 -->
            <div class="capture-item">
              <div class="capture-header">
                <span class="capture-label">出场抓拍</span>
                <el-button 
                  size="small" 
                  @click="capturePhoto('exit')"
                  :disabled="!cameras.exit.playing"
                >
                  <el-icon><Camera /></el-icon>
                  抓拍
                </el-button>
              </div>
              <div class="capture-content">
                <div v-if="currentPhotos.exit" class="photo-display">
                  <img :src="currentPhotos.exit.thumbnail" alt="出场抓拍" />
                  <div class="photo-overlay">
                    <div class="photo-time">{{ formatPhotoTime(currentPhotos.exit.timestamp) }}</div>
                    <div v-if="currentPhotos.exit.plateNumber" class="photo-plate">
                      {{ currentPhotos.exit.plateNumber }}
                    </div>
                  </div>
                  <el-button 
                    class="photo-delete" 
                    size="small" 
                    type="danger" 
                    @click.stop="deletePhoto('exit')"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div v-else class="no-capture">
                  <el-icon><Picture /></el-icon>
                  <p>点击抓拍按钮<br>进行抓拍</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="right-panel">
        <!-- 实时称重区 -->
        <div class="weight-section-right">
          <div class="weight-display">
            <div class="weight-header">
              <h4>实时称重</h4>
              <el-tag :type="weightStatus.type" size="small">{{ weightStatus.text }}</el-tag>
            </div>
            <div class="weight-value" :class="weightClass">
              <span class="number">{{ formatWeight(currentWeight) }}</span>
              <span class="unit">吨</span>
            </div>
          </div>
        </div>

        <!-- 车牌输入区 -->
        <div class="plate-section">
          <div class="plate-header">
            <h4>车牌识别</h4>
            <el-switch v-model="autoRecognition" active-text="自动" inactive-text="手动" />
          </div>
          
          <!-- 车牌显示 -->
          <div class="plate-display">
            <div class="china-plate" 
                 :class="[plateValidClass, plateData.isNewEnergy ? 'new-energy' : 'normal']"
                 @click="openPlateDialog"
                 :style="{ cursor: !autoRecognition ? 'pointer' : 'default' }"
            >
              <div class="plate-header" :class="plateData.isNewEnergy ? 'plate-header-green' : 'plate-header-blue'">
                {{ plateData.isNewEnergy ? '新能源车牌' : '民用车牌' }}
              </div>
              <div class="plate-content">
                <div class="plate-chars">
                  <div class="char-box province-char">
                    {{ plateData.province || '省' }}
                  </div>
                  <div class="char-separator">●</div>
                  <div class="char-box area-char">
                    {{ plateData.area || 'A' }}
                  </div>
                  <div class="char-box number-char" v-for="(char, index) in displayNumbers" :key="index">
                    {{ char }}
                  </div>
                </div>
              </div>
              <div class="plate-bottom">
                <div class="plate-code">{{ fullPlateNumber }}</div>
                <div class="plate-hint" v-if="!autoRecognition">
                  <el-icon><Edit /></el-icon>
                  点击编辑
                </div>
              </div>
            </div>
            
            <div class="plate-controls">
              <div class="plate-status">
                <el-icon :class="plateValidClass">
                  <CircleCheckFilled v-if="plateValid" />
                  <CircleCloseFilled v-else />
                </el-icon>
                <span>{{ plateStatusText }}</span>
              </div>
              
              <div class="plate-type-toggle" v-if="!autoRecognition">
                <el-button 
                  size="small" 
                  :type="!plateData.isNewEnergy ? 'primary' : 'default'"
                  @click="setPlateType(false)"
                >
                  蓝牌
                </el-button>
                <el-button 
                  size="small" 
                  :type="plateData.isNewEnergy ? 'success' : 'default'"
                  @click="setPlateType(true)"
                >
                  绿牌
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作控制区 -->
        <div class="control-section">
          <div class="vehicle-status">
            <h4>车辆状态</h4>
            <el-radio-group v-model="vehicleStatus" size="large">
              <el-radio-button value="IN">入场</el-radio-button>
              <el-radio-button value="OUT">出场</el-radio-button>
            </el-radio-group>
          </div>

          <div class="main-actions">
            <el-button 
              type="success" 
              size="large" 
              :disabled="!canSubmit"
              @click="submitData"
              :loading="submitting"
            >
              <el-icon><Upload /></el-icon>
              {{ submitting ? '提交中...' : '提交数据' }}
            </el-button>
            
            <el-button 
              type="primary" 
              size="large"
              :disabled="!canTriggerGate"
              @click="triggerGate"
              :loading="gateTriggering"
            >
              <el-icon><Unlock /></el-icon>
              {{ gateTriggering ? '抬杆中...' : '抬杆放行' }}
            </el-button>

            <!-- 重置系统按钮已隐藏 -->
            <!-- <el-button 
              type="warning" 
              size="large"
              @click="resetSystem"
            >
              <el-icon><RefreshLeft /></el-icon>
              重置系统
            </el-button> -->
          </div>
          
          <!-- 项目信息 -->
          <div class="project-info-panel">
            <div class="info-header">
              <h4>项目信息</h4>
            </div>
            <div class="info-content">
              <!-- 基础信息区 -->
              <div class="basic-info">
                <div class="info-row">
                  <span class="info-label">项目:</span>
                  <span class="info-value">{{ projectInfo.projectName }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">标段:</span>
                  <span class="info-value">{{ projectInfo.sectionName }}</span>
                  <span class="info-divider">|</span>
                  <span class="info-label">类型:</span>
                  <span class="info-value">{{ projectInfo.projectType }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">位置:</span>
                  <span class="info-value">{{ projectInfo.projectLocation }}</span>
                </div>
              </div>
              
              <!-- 进度信息区 -->
              <div class="progress-info">
                <div class="progress-numbers">
                  <span class="control-info">控制: {{ formatNumber(projectInfo.controlTotal) }}吨</span>
                  <span class="extracted-info">已采: {{ formatNumber(projectInfo.extractedTotal) }}吨</span>
                </div>
                <div class="progress-bar-container">
                  <div class="progress-bar" :class="getProgressClass(progressPercent)">
                    <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
                  </div>
                  <span class="progress-text">{{ progressPercent }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近记录 - 已隐藏 -->
        <!-- <div class="recent-records" style="display: none;">
          <div class="records-header">
            <h4>最近记录</h4>
            <el-link @click="showHistoryDialog = true" type="primary">查看更多</el-link>
          </div>
          <div class="records-list">
            <div 
              v-for="record in recentRecords"
              :key="record.id"
              class="record-item"
            >
              <el-tag size="small" :type="record.status === 'IN' ? 'success' : 'warning'">
                {{ record.plateNumber }}
              </el-tag>
              <span class="record-weight">{{ record.weight }}吨</span>
              <span class="record-time">{{ formatRecordTime(record.timestamp) }}</span>
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 底部快捷操作栏 -->
    <div class="bottom-toolbar" :class="{ 'fullscreen-toolbar': isFullscreen }">
      <div class="toolbar-left">
        <!-- 全屏模式下显示系统标题 -->
        <div v-if="isFullscreen" class="system-title-compact">
          <img src="/logo.svg" alt="系统Logo" class="mini-logo" />
          <span class="title-text">河北省采(弃)砂监管一体机</span>
        </div>
        
        <div class="system-status">
          <el-tag :type="systemStatusType" size="small">{{ systemStatusText }}</el-tag>
          <span class="status-details">{{ statusDetails }}</span>
        </div>
        
        <!-- 全屏模式下显示设备状态 -->
        <div v-if="isFullscreen" class="device-status-compact">
          <StatusIndicator 
            v-for="device in deviceList"
            :key="device.name"
            :status="device.status"
            :label="device.label"
            :icon="device.icon"
            size="mini"
          />
        </div>
      </div>
      
      <!-- 全屏模式下显示时间信息 -->
      <div v-if="isFullscreen" class="toolbar-center">
        <div class="time-display">
          <div class="current-time-compact">{{ currentTime }}</div>
          <div class="current-date-compact">{{ currentDate }}</div>
        </div>
      </div>
      
      <div class="toolbar-right">
        <!-- 全屏模式下的控制按钮 -->
        <div v-if="isFullscreen" class="fullscreen-controls">
          <ThemeToggle />
        </div>
        
        <el-button size="small" @click="showHistoryDialog = true">
          <el-icon><Document /></el-icon>
          历史记录
        </el-button>
        <!-- 数据导出按钮 - 已隐藏 -->
        <!-- <el-button size="small" @click="exportData">
          <el-icon><Download /></el-icon>
          数据导出
        </el-button> -->
        <el-button size="small" @click="showSettings = true">
          <el-icon><Tools /></el-icon>
          系统设置
        </el-button>
      </div>
    </div>

    <!-- 设置对话框 -->
    <SettingsDialog v-model="showSettings" />
    
    <!-- 历史记录对话框 -->
    <HistoryDialog v-model="showHistoryDialog" />

    <!-- 车牌输入对话框 -->
    <el-dialog 
      v-model="plateDialogVisible" 
      :title="getDialogTitle"
      width="500px" 
      center
      :before-close="cancelPlateEdit"
    >
      <div class="plate-input-dialog">
        <!-- 车牌预览 -->
        <div class="plate-preview">
          <div class="china-plate-large" :class="tempPlateData.isNewEnergy ? 'new-energy' : 'normal'">
            <div class="plate-header" :class="tempPlateData.isNewEnergy ? 'plate-header-green' : 'plate-header-blue'">
              {{ tempPlateData.isNewEnergy ? '新能源车牌' : '民用车牌' }}
            </div>
            <div class="plate-content">
              <div class="plate-chars">
                <div class="char-box province-char">
                  {{ tempPlateData.province || '省' }}
                </div>
                <div class="char-separator">●</div>
                <div class="char-box area-char">
                  {{ tempPlateData.area || 'A' }}
                </div>
                <div class="char-box number-char" v-for="(char, index) in tempDisplayNumbers" :key="index">
                  {{ char }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史车牌选择 -->
        <div v-if="plateInputStep === 0" class="history-section">
          <div class="section-title">
            <el-icon><Clock /></el-icon>
            历史车牌记录
          </div>
          <div class="history-plates">
            <div 
              v-for="(record, index) in sortedPlateHistory" 
              :key="index"
              class="history-plate-item"
              @click="selectHistoryPlate(record)"
            >
              <div class="history-plate-info">
                <div class="plate-number-small" :class="record.isNewEnergy ? 'green' : 'blue'">
                  {{ record.plateNumber }}
                </div>
                <div class="plate-type-small">
                  {{ record.isNewEnergy ? '新能源' : '民用' }}
                </div>
              </div>
              <div class="last-used">
                {{ formatLastUsed(record.lastUsed) }}
              </div>
            </div>
          </div>
          <div class="history-actions">
            <el-button @click="startManualInput" type="primary">
              <el-icon><Plus /></el-icon>
              手动输入新车牌
            </el-button>
          </div>
        </div>

        <!-- 车牌类型选择 -->
        <div v-if="plateInputStep === 1" class="plate-type-section">
          <div class="section-title">选择车牌类型</div>
          <el-radio-group v-model="tempPlateData.isNewEnergy" size="large">
            <el-radio-button :value="false">民用车牌（蓝牌）</el-radio-button>
            <el-radio-button :value="true">新能源车牌（绿牌）</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 省份选择 -->
        <div v-if="plateInputStep === 1" class="input-section">
          <div class="section-title">省份简称</div>
          <div class="province-grid">
            <el-button 
              v-for="province in allProvinces"
              :key="province"
              size="small"
              :type="tempPlateData.province === province ? 'primary' : 'default'"
              @click="selectTempProvince(province)"
              :class="[
                'province-btn', 
                { 'hebei-highlight': province === '冀' && !tempPlateData.province }
              ]"
            >
              {{ province }}
            </el-button>
          </div>
        </div>

        <!-- 地区代码选择 -->
        <div v-if="plateInputStep === 2" class="input-section">
          <div class="section-title">地区代码</div>
          <div class="area-grid">
            <el-button 
              v-for="area in areaLetters"
              :key="area"
              size="small"
              :type="tempPlateData.area === area ? 'primary' : 'default'"
              @click="selectTempArea(area)"
              class="area-btn"
            >
              {{ area }}
            </el-button>
          </div>
        </div>

        <!-- 号码输入 -->
        <div v-if="plateInputStep === 3" class="input-section">
          <div class="section-title">
            车牌号码 ({{ tempPlateData.numbers?.length || 0 }}/{{ tempPlateData.isNewEnergy ? '6' : '5' }})
          </div>
          <div class="number-grid">
            <el-button 
              v-for="char in numberChars"
              :key="char"
              size="small"
              @click="inputTempChar(char)"
              class="number-btn"
              :disabled="tempPlateData.numbers?.length >= (tempPlateData.isNewEnergy ? 6 : 5)"
            >
              {{ char }}
            </el-button>
          </div>
          <div class="dialog-control-buttons">
            <el-button size="default" type="warning" @click="deleteTempLast" :disabled="!tempPlateData.numbers?.length">
              <el-icon><ArrowLeft /></el-icon>
              删除
            </el-button>
            <el-button size="default" type="info" @click="clearTempNumbers">
              <el-icon><RefreshLeft /></el-icon>
              清空号码
            </el-button>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="plateInputStep > 0" @click="goToPrevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button @click="cancelPlateEdit">取消</el-button>
          <el-button 
            v-if="plateInputStep < 3" 
            type="primary" 
            @click="goToNextStep"
            :disabled="!canGoToNextStep"
          >
            下一步
            <el-icon><ArrowRight /></el-icon>
          </el-button>
          <el-button 
            v-if="plateInputStep === 3" 
            type="primary" 
            @click="confirmPlateEdit" 
            :disabled="!isTempPlateValid"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
// 双摄像头一体机终端系统
// 作者：仕伟

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import StatusIndicator from '@/components/StatusIndicator.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import SettingsDialog from '@/components/SettingsDialog.vue'
import HistoryDialog from '@/components/HistoryDialog.vue'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/services/websocket'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
import { formatSmartTime, getRelativeTime } from '@/utils/timeFormatter'
import { 
  VideoCamera, 
  Camera,
  Upload, 
  Unlock, 
  RefreshLeft, 
  Document, 
  Download, 
  Tools,
  View,
  Delete,
  Close,
  Picture,
  CircleCheckFilled,
  CircleCloseFilled,
  ArrowLeft,
  ArrowRight,
  Edit,
  Clock,
  Plus
} from '@element-plus/icons-vue'

// Store
const deviceStore = useDeviceStore()

// WebSocket
const { connect, subscribe, isConnected } = useWebSocket()

// 强制性全屏模式 - 始终启用内置全屏
const isFullscreen = ref(true) // 强制设为true，不可切换

// 双摄像头状态
const cameras = reactive({
  entrance: {
    online: true,
    playing: false,
    resolution: '1920x1080',
    fps: '25',
    poster: '/video-poster.jpg'
  },
  exit: {
    online: true,
    playing: false,
    resolution: '1920x1080', 
    fps: '25',
    poster: '/video-poster.jpg'
  }
})

// 当前抓拍图片（进场和出场各一张）
const currentPhotos = reactive({
  entrance: null, // 进场抓拍
  exit: null      // 出场抓拍
})


// 设备列表
const deviceList = computed(() => {
  // 地磅状态基于WebSocket连接状态
  const wsConnected = isConnected('/weight')
  const scaleStatus = wsConnected ? 'online' : 'offline'
  
  return [
    { name: 'camera', label: '摄像头', icon: 'VideoCamera', status: deviceStore.status.camera },
    { name: 'scale', label: '地磅', icon: 'Scale', status: scaleStatus },
    { name: 'gate', label: '道闸', icon: 'Operation', status: deviceStore.status.gate }
  ]
})

// 系统时间
const currentTime = ref('')
const currentDate = ref('')

// 称重数据
const currentWeight = ref(0)
const weightStable = ref(true)

// 车牌数据
const plateData = reactive({ province: '', area: '', numbers: '', isNewEnergy: false })
const plateValid = ref(false)
const autoRecognition = ref(true)

// 车牌弹框相关
const plateDialogVisible = ref(false)
const tempPlateData = reactive({ province: '', area: '', numbers: '', isNewEnergy: false })
const plateInputStep = ref(1) // 1:省份 2:地区代码 3:号码

// 历史车牌记录
const plateHistory = ref([
  { plateNumber: '京A12345', isNewEnergy: false, lastUsed: Date.now() - 1000 * 60 * 5 },
  { plateNumber: '沪BD12345', isNewEnergy: true, lastUsed: Date.now() - 1000 * 60 * 30 },
  { plateNumber: '粤C88888', isNewEnergy: false, lastUsed: Date.now() - 1000 * 60 * 60 }
])

// 车辆状态
const vehicleStatus = ref<'IN' | 'OUT'>('IN')

// 项目信息数据接口定义
interface ProjectInfo {
  projectName: string      // 项目名称
  sectionName: string     // 标段名称
  projectLocation: string // 项目位置
  projectType: string     // 项目类型
  controlTotal: number    // 控制总量(吨)
  extractedTotal: number  // 已采总量(吨)
}

// 项目信息数据 - 模拟数据，后续将从API获取
const projectInfo = ref<ProjectInfo>({
  projectName: '河北青龙湖国家湿地公园建设工程—河道治理及修复工程',
  sectionName: '',
  projectLocation: '秦皇岛市青龙满族自治县',
  projectType: '弃砂综合利用',
  controlTotal: 50000,     // 控制总量50,000吨
  extractedTotal: 40000    // 已采总量40,000吨
})

// 操作状态
const submitting = ref(false)
const gateTriggering = ref(false)

// 对话框状态
const showSettings = ref(false)
const showHistoryDialog = ref(false)

// 完整省份列表（河北省冀字排在前列）
const allProvinces = ['冀', '京', '沪', '津', '渝', '鲁', '苏', '浙', '粤', '闽', '湘', '鄂', '豫', '皖', '赣', '川', '贵', '云', '陕', '甘', '青', '蒙', '桂', '琼', '宁', '新', '藏', '港', '澳', '台', '黑', '吉', '辽']

// 地区字母（A-Z，去除I和O避免混淆）
const areaLetters = ['A','B','C','D','E','F','G','H','J','K','L','M','N','P','Q','R','S','T','U','V','W','X','Y','Z']

// 号码字符（数字+字母，去除I和O）
const numberChars = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','J','K','L','M','N','P','Q','R','S','T','U','V','W','X','Y','Z']

// 计算属性
const allCamerasPlaying = computed(() => cameras.entrance.playing && cameras.exit.playing)

const plateNumber = computed(() => `${plateData.province}${plateData.area}${plateData.numbers}`)

const displayNumbers = computed(() => {
  const nums = plateData.numbers || ''
  const maxLength = plateData.isNewEnergy ? 6 : 5
  const result = []
  for (let i = 0; i < maxLength; i++) {
    result.push(nums[i] || '')
  }
  return result
})

const tempDisplayNumbers = computed(() => {
  const nums = tempPlateData.numbers || ''
  const maxLength = tempPlateData.isNewEnergy ? 6 : 5
  const result = []
  for (let i = 0; i < maxLength; i++) {
    result.push(nums[i] || '')
  }
  return result
})

const fullPlateNumber = computed(() => {
  if (!plateData.province || !plateData.area || !plateData.numbers) return ''
  return `${plateData.province}${plateData.area}${plateData.numbers}`
})

const plateValidClass = computed(() => plateValid.value ? 'valid' : 'invalid')

const plateStatusText = computed(() => {
  if (!plateData.province) return '请选择省份'
  if (!plateData.area) return '请选择地区代码'
  const expectedLength = plateData.isNewEnergy ? 6 : 5
  if (!plateData.numbers || plateData.numbers.length < expectedLength) {
    return `请输入完整车牌号码（${plateData.numbers?.length || 0}/${expectedLength}）`
  }
  return plateValid.value ? '车牌格式正确' : '请检查车牌格式'
})

const isTempPlateValid = computed(() => {
  const expectedLength = tempPlateData.isNewEnergy ? 6 : 5
  return tempPlateData.province && 
         tempPlateData.area && 
         tempPlateData.numbers && 
         tempPlateData.numbers.length === expectedLength
})

// 历史车牌排序（按最后使用时间）
const sortedPlateHistory = computed(() => {
  return plateHistory.value.slice().sort((a, b) => b.lastUsed - a.lastUsed)
})

// 弹框标题
const getDialogTitle = computed(() => {
  if (plateInputStep.value === 0) return '选择车牌'
  if (plateInputStep.value === 1) return '第1步: 省份简称'
  if (plateInputStep.value === 2) return '第2步: 地区代码'
  if (plateInputStep.value === 3) return '第3步: 车牌号码'
  return '车牌输入'
})

// 下一步按钮可用性
const canGoToNextStep = computed(() => {
  if (plateInputStep.value === 1) return tempPlateData.province
  if (plateInputStep.value === 2) return tempPlateData.area
  return false
})

const weightClass = computed(() => {
  if (currentWeight.value > 50) return 'overload'
  if (currentWeight.value < 5) return 'light'
  return 'normal'
})

const weightStatus = computed(() => {
  // 检查WebSocket连接状态
  const wsConnected = isConnected('/weight')
  if (!wsConnected) {
    return { type: 'danger', text: '连接断开' }
  }
  
  // 检查称重数据状态
  if (!weightStable.value) return { type: 'warning', text: '不稳定' }
  if (currentWeight.value > 50) return { type: 'danger', text: '超重' }
  if (currentWeight.value === 0) return { type: 'info', text: '等待数据' }
  return { type: 'success', text: '正常' }
})

const canSubmit = computed(() => {
  return plateValid.value && weightStable.value && currentWeight.value > 0
})

const canTriggerGate = computed(() => canSubmit.value)

const systemStatusType = computed(() => {
  if (deviceStore.overallStatus === 'online') return 'success'
  if (deviceStore.overallStatus === 'warning') return 'warning'
  return 'danger'
})

const systemStatusText = computed(() => {
  const statusMap = {
    online: '系统正常',
    warning: '部分异常', 
    offline: '系统离线',
    error: '系统错误'
  }
  return statusMap[deviceStore.overallStatus] || '未知状态'
})

const statusDetails = computed(() => {
  return `设备在线: ${deviceStore.onlineCount}/${deviceStore.totalCount}`
})

// 摄像头方法
const toggleCamera = (cameraType: 'entrance' | 'exit') => {
  const camera = cameras[cameraType]
  camera.playing = !camera.playing
  const cameraName = cameraType === 'entrance' ? '进场摄像头' : '出场摄像头'
  ElMessage.success(camera.playing ? `${cameraName}开始监控` : `${cameraName}停止监控`)
}

const toggleAllCameras = () => {
  const shouldPlay = !allCamerasPlaying.value
  cameras.entrance.playing = shouldPlay
  cameras.exit.playing = shouldPlay
  ElMessage.success(shouldPlay ? '所有摄像头开始监控' : '所有摄像头停止监控')
}

// 模拟生成抓拍图片
const generateMockPhoto = (cameraType: 'entrance' | 'exit') => {
  const canvas = document.createElement('canvas')
  canvas.width = 400
  canvas.height = 300
  const ctx = canvas.getContext('2d')!
  
  // 背景色
  const bgColor = cameraType === 'entrance' ? '#4a90e2' : '#f5a623'
  ctx.fillStyle = bgColor
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 添加网格线模拟监控画面
  ctx.strokeStyle = 'rgba(255,255,255,0.3)'
  ctx.lineWidth = 1
  for (let i = 0; i < canvas.width; i += 20) {
    ctx.beginPath()
    ctx.moveTo(i, 0)
    ctx.lineTo(i, canvas.height)
    ctx.stroke()
  }
  for (let i = 0; i < canvas.height; i += 20) {
    ctx.beginPath()
    ctx.moveTo(0, i)
    ctx.lineTo(canvas.width, i)
    ctx.stroke()
  }
  
  // 添加标题
  ctx.fillStyle = 'white'
  ctx.font = 'bold 24px Arial'
  ctx.textAlign = 'center'
  const title = cameraType === 'entrance' ? '进场监控' : '出场监控'
  ctx.fillText(title, canvas.width/2, 50)
  
  // 添加时间戳
  ctx.font = '16px Arial'
  const timeStr = new Date().toLocaleString('zh-CN')
  ctx.fillText(timeStr, canvas.width/2, 80)
  
  // 添加车牌信息
  if (plateNumber.value && plateNumber.value.length > 6) {
    ctx.font = 'bold 20px Arial'
    ctx.fillStyle = '#ffff00'
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.strokeText(plateNumber.value, canvas.width/2, canvas.height/2)
    ctx.fillText(plateNumber.value, canvas.width/2, canvas.height/2)
  }
  
  // 添加重量信息
  ctx.fillStyle = 'white'
  ctx.font = '18px Arial'
  const weightText = `重量: ${currentWeight.value.toFixed(1)}吨`
  ctx.fillText(weightText, canvas.width/2, canvas.height/2 + 40)
  
  // 添加摄像头标识
  ctx.fillStyle = 'rgba(0,0,0,0.7)'
  ctx.fillRect(10, canvas.height - 40, 100, 30)
  ctx.fillStyle = 'white'
  ctx.font = '12px Arial'
  ctx.textAlign = 'left'
  ctx.fillText(`CAM-${cameraType.toUpperCase()}`, 15, canvas.height - 20)
  
  return canvas.toDataURL('image/jpeg', 0.8)
}

const capturePhoto = (cameraType: 'entrance' | 'exit') => {
  const cameraName = cameraType === 'entrance' ? '进场监控' : '出场监控'
  
  // 生成模拟图片
  const mockImage = generateMockPhoto(cameraType)
  
  const photoData = {
    id: Date.now() + Math.random(),
    camera: cameraType,
    cameraName,
    thumbnail: mockImage,
    fullUrl: mockImage,
    timestamp: Date.now(),
    plateNumber: plateNumber.value || null
  }
  
  // 更新当前抓拍（覆盖之前的）
  currentPhotos[cameraType] = photoData
  
  ElMessage.success(`${cameraName}抓拍成功`)
}

const captureAllPhotos = () => {
  if (cameras.entrance.playing) capturePhoto('entrance')
  if (cameras.exit.playing) capturePhoto('exit')
  
  if (!cameras.entrance.playing && !cameras.exit.playing) {
    ElMessage.warning('请先开启摄像头监控')
  }
}


const deletePhoto = (cameraType: 'entrance' | 'exit') => {
  if (currentPhotos[cameraType]) {
    currentPhotos[cameraType] = null
    const cameraName = cameraType === 'entrance' ? '进场' : '出场'
    ElMessage.success(`${cameraName}抓拍图片已删除`)
  }
}

const clearAllPhotos = () => {
  if (!currentPhotos.entrance && !currentPhotos.exit) {
    ElMessage.warning('没有图片需要清空')
    return
  }
  
  currentPhotos.entrance = null
  currentPhotos.exit = null
  ElMessage.success('已清空所有抓拍图片')
}


// 时间格式化方法
const formatPhotoTime = (timestamp: number) => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

const formatDateTime = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 其他方法
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY年MM月DD日 dddd')
}

const formatWeight = (weight: number) => weight.toFixed(1)

// 计算进度百分比
const progressPercent = computed(() => {
  const percent = (projectInfo.value.extractedTotal / projectInfo.value.controlTotal) * 100
  return Math.min(Math.round(percent), 100) // 限制最大100%
})

// 格式化数字显示（添加千分位分隔符）
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN')
}

// 根据进度获取样式类名
const getProgressClass = (percent: number) => {
  if (percent >= 90) return 'progress-danger'   // 红色：超限警告
  if (percent >= 70) return 'progress-warning'  // 橙色：接近上限
  return 'progress-normal'                      // 绿色：正常范围
}

const formatRecordTime = (timestamp: number) => {
  return dayjs(timestamp).format('HH:mm')
}


const selectProvince = (province: string) => {
  plateData.province = province
  validatePlate()
}

const selectArea = (area: string) => {
  plateData.area = area
  validatePlate()
}

const inputChar = (char: string) => {
  if (plateData.numbers.length < 5) {
    plateData.numbers += char
    validatePlate()
  }
}

const deleteLast = () => {
  if (plateData.numbers.length > 0) {
    plateData.numbers = plateData.numbers.slice(0, -1)
    validatePlate()
  }
}

const clearPlate = () => {
  plateData.province = ''
  plateData.area = ''
  plateData.numbers = ''
  plateData.isNewEnergy = false
  plateValid.value = false
}

const validatePlate = () => {
  const expectedLength = plateData.isNewEnergy ? 6 : 5
  if (plateData.province && plateData.area && plateData.numbers && plateData.numbers.length === expectedLength) {
    // 模拟车牌格式验证
    const fullPlate = plateNumber.value
    // 验证长度：民用车牌7位，新能源车牌8位
    const totalExpectedLength = plateData.isNewEnergy ? 8 : 7
    plateValid.value = fullPlate.length === totalExpectedLength && Math.random() > 0.1
  } else {
    plateValid.value = false
  }
}

// 车牌弹框方法
const openPlateDialog = () => {
  if (!autoRecognition.value) {
    // 复制当前车牌数据到临时数据
    tempPlateData.province = plateData.province
    tempPlateData.area = plateData.area
    tempPlateData.numbers = plateData.numbers
    tempPlateData.isNewEnergy = plateData.isNewEnergy
    
    // 重置步骤为历史车牌选择
    plateInputStep.value = 0
    plateDialogVisible.value = true
  }
}

const setPlateType = (isNewEnergy: boolean) => {
  plateData.isNewEnergy = isNewEnergy
  // 如果切换到新能源车牌且当前号码超过6位，截断到6位
  if (isNewEnergy && plateData.numbers.length > 6) {
    plateData.numbers = plateData.numbers.substring(0, 6)
  }
  // 如果切换到民用车牌且当前号码超过5位，截断到5位
  if (!isNewEnergy && plateData.numbers.length > 5) {
    plateData.numbers = plateData.numbers.substring(0, 5)
  }
  validatePlate()
}

// 旧的临时车牌输入方法已移动到后面

const inputTempChar = (char: string) => {
  const maxLength = tempPlateData.isNewEnergy ? 6 : 5
  if (tempPlateData.numbers.length < maxLength) {
    tempPlateData.numbers += char
  }
}

const deleteTempLast = () => {
  if (tempPlateData.numbers.length > 0) {
    tempPlateData.numbers = tempPlateData.numbers.slice(0, -1)
  }
}

const clearTempPlate = () => {
  tempPlateData.province = ''
  tempPlateData.area = ''
  tempPlateData.numbers = ''
}

const clearTempNumbers = () => {
  tempPlateData.numbers = ''
}

// 历史车牌相关方法
const selectHistoryPlate = (record: any) => {
  // 解析车牌号
  const plateNumber = record.plateNumber
  tempPlateData.province = plateNumber.charAt(0)
  tempPlateData.area = plateNumber.charAt(1)
  tempPlateData.numbers = plateNumber.substring(2)
  tempPlateData.isNewEnergy = record.isNewEnergy
  
  // 直接确认使用历史车牌
  confirmPlateEdit()
}

const startManualInput = () => {
  // 清空临时数据并进入手动输入第一步
  tempPlateData.province = ''
  tempPlateData.area = ''
  tempPlateData.numbers = ''
  tempPlateData.isNewEnergy = false
  plateInputStep.value = 1
}

const formatLastUsed = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚使用'
}

// 分步骤导航方法
const goToNextStep = () => {
  if (plateInputStep.value < 3) {
    plateInputStep.value++
  }
}

const goToPrevStep = () => {
  if (plateInputStep.value > 1) {
    plateInputStep.value--
  } else {
    plateInputStep.value = 0 // 回到历史车牌选择
  }
}

// 更新临时车牌选择方法以自动进入下一步
const selectTempProvince = (province: string) => {
  tempPlateData.province = province
  // 自动进入下一步
  setTimeout(() => {
    goToNextStep()
  }, 200)
}

const selectTempArea = (area: string) => {
  tempPlateData.area = area
  // 自动进入下一步
  setTimeout(() => {
    goToNextStep()
  }, 200)
}

// 保存车牌到历史记录
const saveToHistory = (plateNum: string, isNewEnergy: boolean) => {
  // 检查是否已存在
  const existingIndex = plateHistory.value.findIndex(item => item.plateNumber === plateNum)
  
  if (existingIndex >= 0) {
    // 更新使用时间
    plateHistory.value[existingIndex].lastUsed = Date.now()
  } else {
    // 添加新记录
    plateHistory.value.unshift({
      plateNumber: plateNum,
      isNewEnergy,
      lastUsed: Date.now()
    })
    
    // 限制历史记录最多10个
    if (plateHistory.value.length > 10) {
      plateHistory.value = plateHistory.value.slice(0, 10)
    }
  }
}

const confirmPlateEdit = () => {
  // 将临时数据复制到正式数据
  plateData.province = tempPlateData.province
  plateData.area = tempPlateData.area
  plateData.numbers = tempPlateData.numbers
  plateData.isNewEnergy = tempPlateData.isNewEnergy
  
  // 保存到历史记录
  const fullPlateNumber = `${tempPlateData.province}${tempPlateData.area}${tempPlateData.numbers}`
  saveToHistory(fullPlateNumber, tempPlateData.isNewEnergy)
  
  validatePlate()
  plateDialogVisible.value = false
  ElMessage.success('车牌信息已更新')
}

const cancelPlateEdit = () => {
  plateDialogVisible.value = false
  plateInputStep.value = 0
}

const submitData = async () => {
  submitting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据提交成功')
    
    // 如果是出场车辆，更新项目已采总量
    if (vehicleStatus.value === 'OUT' && currentWeight.value > 0) {
      projectInfo.value.extractedTotal += currentWeight.value
      
      // 确保已采总量不超过控制总量的110%（允许小幅超限）
      const maxExtracted = projectInfo.value.controlTotal * 1.1
      if (projectInfo.value.extractedTotal > maxExtracted) {
        projectInfo.value.extractedTotal = maxExtracted
      }
      
      // 根据进度显示不同提示
      const newPercent = (projectInfo.value.extractedTotal / projectInfo.value.controlTotal) * 100
      if (newPercent >= 100) {
        ElMessage.warning(`项目已达控制总量限制！当前完成度: ${Math.round(newPercent)}%`)
      } else if (newPercent >= 90) {
        ElMessage.warning(`项目接近控制总量！当前完成度: ${Math.round(newPercent)}%`)
      }
    }
    
    // 重置系统
    resetSystem()
  } catch (error) {
    ElMessage.error('数据提交失败')
  } finally {
    submitting.value = false
  }
}

const triggerGate = async () => {
  gateTriggering.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('抬杆成功，请通行')
  } catch (error) {
    ElMessage.error('抬杆失败')
  } finally {
    gateTriggering.value = false
  }
}

const resetSystem = () => {
  plateData.province = ''
  plateData.numbers = ''
  plateValid.value = false
  currentWeight.value = 0
  ElMessage.info('系统已重置')
}

const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 生命周期
onMounted(async () => {
  // 更新时间
  updateTime()
  setInterval(updateTime, 1000)
  
  // 内置全屏模式已强制启用，无需自动进入浏览器全屏
  
  // 连接WebSocket
  await connect()
  
  // 订阅数据更新
  subscribe('weight_data', (data) => {
    currentWeight.value = data.weight
    weightStable.value = data.status === 'normal'
  })
  
  subscribe('plate_recognition', (data) => {
    if (autoRecognition.value) {
      const [province, ...numbers] = data.plateNumber.split('')
      plateData.province = province
      plateData.numbers = numbers.join('')
      validatePlate()
    }
  })
  
  // 初始化设备监控
  deviceStore.initDeviceMonitoring()
  
  // 系统启动完成提示
  ElMessage.success('一体机系统启动完成')
})
</script>

<style scoped lang="scss">
.terminal-system {
  /* 使用 100dvh 适配不同系统窗口高度 */
  height: 100dvh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  /* 顶层隐藏溢出，内部工作区自行滚动 */
  overflow: hidden;

  &.fullscreen-mode {
    .system-statusbar {
      display: none;
    }
    
    .terminal-workspace {
      /* 全屏模式下按可视高度自适应 */
      height: auto;
      min-height: 0;
    }

    // 全屏模式下抓拍图片区域适配
    .current-captures {
      /* 与视口解耦，按内容自适应 */
      max-height: none;
    
      .capture-content {
        .photo-display {
          /* 高度由比例控制 */
        }
      }
    }

    // 全屏模式下双摄像头监控区域调整
    .left-panel .dual-monitor {
      /* 与视口解耦，按内容高度自适应 */
      height: auto;
    }

    // 全屏模式下最近记录区域调整
    .right-panel .recent-records {
      max-height: clamp(220px, 36vh, 460px); // 自适应最近记录高度
    }
  }

  .system-statusbar {
    height: 60px;
    background: var(--bg-header);
    border-bottom: 2px solid var(--el-color-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    box-shadow: var(--shadow-base);

    .status-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-xl);

      .system-logo {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        img {
          width: 36px;
          height: 36px;
        }

        .system-title {
          font-size: 20px;
          font-weight: 700;
          color: var(--text-primary);
          background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .device-status {
        display: flex;
        gap: var(--spacing-md);
      }
    }

    .status-right {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);

      .system-info {
        text-align: right;

        .current-time {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
        }

        .current-date {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }

      .system-controls {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }

  .terminal-workspace {
    flex: 1 1 auto;
    min-height: 0; /* 允许内部滚动 */
    display: grid;
    grid-template-columns: minmax(480px, 1fr) minmax(360px, 560px);
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    overflow: auto; /* 关键：小屏允许滚动 */

    .left-panel, .right-panel {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .left-panel {
      .dual-monitor {
        /* 高度解耦：由内部视频比例决定 */
        height: auto;
        background: var(--bg-card);
        border-radius: var(--border-radius-large);
        border: 2px solid var(--border-base);
        display: flex;
        flex-direction: column;

        .monitor-header {
          padding: var(--spacing-md);
          border-bottom: 1px solid var(--border-base);
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: var(--bg-secondary);

          h3 {
            margin: 0;
            color: var(--text-primary);
            font-size: 16px;
          }
        }

        .cameras-grid {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--spacing-sm);
          padding: var(--spacing-md);

          .camera-item {
            display: flex;
            flex-direction: column;
            background: var(--bg-secondary);
            border-radius: var(--border-radius-base);
            border: 1px solid var(--border-base);

            .camera-header {
              padding: var(--spacing-sm);
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-bottom: 1px solid var(--border-base);
              font-size: 12px;

              .camera-label {
                font-weight: 600;
                color: var(--text-primary);
              }

              .camera-status {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 11px;
                color: var(--text-secondary);

                .status-dot {
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background: var(--el-color-danger);
                }

                &.online .status-dot {
                  background: var(--el-color-success);
                  animation: pulse 2s infinite;
                }
              }
            }

            .video-container {
              flex: 1;
              display: flex;
              flex-direction: column;

              .video-player {
                /* 独立于外层尺寸，按比例自适应视频展示，避免被裁切 */
                flex: none;
                width: 100%;
                aspect-ratio: 16 / 9; // 默认16:9，可后续根据视频元数据动态调整
                background: #000;
                border-radius: var(--border-radius-base);
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 160px;

                .video-placeholder {
                  text-align: center;
                  color: #666;

                  .placeholder-icon {
                    font-size: 32px;
                    margin-bottom: var(--spacing-sm);
                  }

                  p {
                    font-size: 12px;
                    margin-bottom: var(--spacing-sm);
                  }
                }

                .video-stream {
                  width: 100%;
                  height: 100%;
                  position: relative;

                  .video-element {
                    width: 100%;
                    height: 100%;
                    object-fit: contain; // 不裁切，自动留黑边
                    background: #000;
                  }

                  .video-overlay {
                    position: absolute;
                    top: var(--spacing-xs);
                    left: var(--spacing-xs);
                    right: var(--spacing-xs);
                    display: flex;
                    justify-content: space-between;

                    .recording-indicator {
                      display: flex;
                      align-items: center;
                      gap: 2px;
                      background: rgba(0,0,0,0.7);
                      color: white;
                      padding: 2px 6px;
                      border-radius: var(--border-radius-base);
                      font-size: 10px;

                      .rec-dot {
                        width: 4px;
                        height: 4px;
                        background: #ff4d4f;
                        border-radius: 50%;
                        animation: pulse 1.5s infinite;
                      }
                    }

                    .video-info {
                      background: rgba(0,0,0,0.7);
                      color: white;
                      padding: 2px 6px;
                      border-radius: var(--border-radius-base);
                      font-size: 9px;
                      text-align: right;
                      
                      div {
                        line-height: 1.1;
                      }
                    }
                  }
                }
              }

              .camera-controls {
                padding: var(--spacing-xs);
                display: flex;
                gap: var(--spacing-xs);
                border-top: 1px solid var(--border-base);

                .el-button {
                  flex: 1;
                  font-size: 11px;
                  padding: 4px 8px;
                }
              }
            }
          }
        }
      }

      .current-captures {
        /* 取消与视口绑定，按内容自适应 */
        background: var(--bg-card);
        border-radius: var(--border-radius-large);
        border: 2px solid var(--border-base);
        padding: var(--spacing-md);
        display: flex;
        flex-direction: column;

        .captures-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-md);

          h4 {
            margin: 0;
            color: var(--text-primary);
            font-size: 14px;
          }

          .captures-actions {
            display: flex;
            gap: var(--spacing-xs);
          }
        }

        .captures-grid {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--spacing-md);
          grid-template-rows: auto;

          .capture-item {
            border: 1px solid var(--border-base);
            border-radius: var(--border-radius-base);
            background: var(--bg-secondary);
            display: flex;
            flex-direction: column;

            .capture-header {
              padding: var(--spacing-sm);
              border-bottom: 1px solid var(--border-base);
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: var(--bg-primary);

              .capture-label {
                font-size: 12px;
                font-weight: 600;
                color: var(--text-primary);
              }
            }

            .capture-content {
              flex: 1;
              padding: var(--spacing-sm);
              display: flex;
              align-items: center;
              justify-content: center;

              .photo-display {
                position: relative;
                width: 100%;
                aspect-ratio: 16 / 9; // 默认按16:9展示，不裁切
                border-radius: var(--border-radius-base);
                overflow: hidden;
                background: #000;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain; // 缩放适配各种尺寸抓拍图
                  display: block;
                  background: #000;
                }

                .photo-overlay {
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  background: linear-gradient(transparent, rgba(0,0,0,0.8));
                  padding: 4px 8px;

                  .photo-time {
                    font-size: 10px;
                    color: white;
                    font-family: 'Courier New', monospace;
                  }

                  .photo-plate {
                    font-size: 11px;
                    color: #ffff00;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                    margin-top: 2px;
                  }
                }

                .photo-delete {
                  position: absolute;
                  top: 4px;
                  right: 4px;
                  width: 18px;
                  height: 18px;
                  padding: 0;
                  border-radius: 50%;
                  opacity: 0;
                  transition: opacity 0.2s;
                  background: rgba(255, 0, 0, 0.8);
                  border: none;
                  color: white;

                  &:hover {
                    background: rgba(255, 0, 0, 1);
                  }
                }

                &:hover .photo-delete {
                  opacity: 1;
                }
              }

              .no-capture {
                text-align: center;
                color: var(--text-secondary);
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .el-icon {
                  font-size: 24px;
                  margin-bottom: var(--spacing-xs);
                }

                p {
                  font-size: 11px;
                  margin: 0;
                  line-height: 1.2;
                }
              }
            }
          }
        }
      }

      .weight-section {
        .weight-display {
          background: var(--bg-card);
          padding: var(--spacing-lg);
          border-radius: var(--border-radius-large);
          border: 2px solid var(--border-base);

          .weight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);

            h4 {
              margin: 0;
              color: var(--text-primary);
            }
          }

          .weight-value {
            text-align: center;
            margin-bottom: var(--spacing-lg);

            .number {
              font-size: 48px;
              font-weight: 700;
              font-family: 'Courier New', monospace;
            }

            .unit {
              font-size: 18px;
              margin-left: var(--spacing-sm);
              color: var(--text-secondary);
            }

            &.normal .number {
              color: var(--el-color-primary);
            }

            &.overload .number {
              color: var(--el-color-danger);
              animation: pulse 1.5s infinite;
            }

            &.light .number {
              color: var(--text-secondary);
            }
          }

        }
      }
    }

    .right-panel {
      .weight-section-right, .plate-section, .control-section, .recent-records {
        background: var(--bg-card);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-large);
        border: 2px solid var(--border-base);
      }

      .weight-section-right {
        .weight-display {
          .weight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);

            h4 {
              margin: 0;
              color: var(--text-primary);
            }
          }

          .weight-value {
            text-align: center;
            margin-bottom: var(--spacing-lg);

            .number {
              font-size: 48px;
              font-weight: 700;
              font-family: 'Courier New', monospace;
            }

            .unit {
              font-size: 18px;
              margin-left: var(--spacing-sm);
              color: var(--text-secondary);
            }

            &.normal .number {
              color: var(--el-color-primary);
            }

            &.overload .number {
              color: var(--el-color-danger);
              animation: pulse 1.5s infinite;
            }

            &.light .number {
              color: var(--text-secondary);
            }
          }

        }
      }

      .plate-section {
        .plate-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-lg);

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        .plate-display {
          text-align: center;
          margin-bottom: var(--spacing-lg);

          .china-plate {
            display: inline-block;
            background: white;
            border: 3px solid #0066cc;
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: var(--spacing-md);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s;

            &.new-energy {
              border-color: #00aa00;
              box-shadow: 0 4px 12px rgba(0,170,0,0.15);
            }

            &.valid {
              border-color: var(--el-color-success);
              box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
              
              &.new-energy {
                border-color: #00dd00;
                box-shadow: 0 4px 12px rgba(0, 221, 0, 0.4);
              }
            }

            &.invalid {
              border-color: var(--el-color-danger);
              box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
            }

            .plate-header-blue {
              background: #0066cc;
              color: white;
              text-align: center;
              font-size: 10px;
              padding: 2px 0;
              margin: -8px -12px 6px -12px;
              border-radius: 5px 5px 0 0;
              font-weight: 500;
            }

            .plate-header-green {
              background: #00aa00;
              color: white;
              text-align: center;
              font-size: 10px;
              padding: 2px 0;
              margin: -8px -12px 6px -12px;
              border-radius: 5px 5px 0 0;
              font-weight: 500;
            }

            .plate-content {
              .plate-chars {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2px;

                .char-box {
                  width: 28px;
                  height: 36px;
                  background: white;
                  border: 1px solid #333;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 20px;
                  font-weight: bold;
                  color: #333;
                  font-family: 'Microsoft YaHei', 'Arial Black', Arial, sans-serif;
                  text-align: center;
                  line-height: 1;
                }

                .province-char {
                  background: linear-gradient(135deg, #0066cc, #004499);
                  color: white;
                  border-color: #0066cc;
                  font-family: 'Microsoft YaHei', 'SimHei', 'Arial Black', Arial, sans-serif;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  text-align: center;
                  vertical-align: middle;
                }

                .area-char {
                  background: #f8f8f8;
                  border-color: #666;
                }

                .number-char {
                  background: #f8f8f8;
                  border-color: #666;
                  
                  &:empty {
                    background: #f0f0f0;
                    border-color: #ccc;
                    border-style: dashed;
                    
                    &::after {
                      content: '';
                      width: 12px;
                      height: 2px;
                      background: #ccc;
    }
  }

  /* 响应式：小屏改为单列布局，内容可滚动 */
  @media (max-width: 1440px) {
    .terminal-workspace {
      grid-template-columns: 1fr;
      grid-auto-rows: min-content;
      overflow: auto;

      .left-panel, .right-panel {
        min-height: 0;
      }

      .left-panel {
        order: 1;
      }

      .right-panel {
        order: 2;
      }

      .left-panel .dual-monitor,
      .left-panel .current-captures,
      .right-panel .weight-section-right,
      .right-panel .recent-records {
        max-height: none;
      }

      .current-captures .captures-grid {
        grid-template-columns: 1fr; /* 小屏抓拍区改为单列，防止拥挤 */
      }

      .left-panel .dual-monitor .cameras-grid {
        grid-template-columns: 1fr; /* 小屏监控区改为单列，避免横向挤压 */
      }
    }
  }

  /* 更小宽度时进一步压缩间距和字号，确保关键模块可见 */
  @media (max-width: 1280px) {
    .system-statusbar .system-title { font-size: 18px; }
    .terminal-workspace { padding: var(--spacing-md); }
  }
}

                .char-separator {
                  color: #0066cc;
                  font-size: 8px;
                  margin: 0 2px;
                  font-weight: bold;
                }
              }
            }

            .plate-bottom {
              margin-top: 6px;
              padding-top: 4px;
              border-top: 1px solid #eee;
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .plate-code {
                font-size: 11px;
                color: #666;
                font-family: 'Courier New', monospace;
                letter-spacing: 1px;
              }

              .plate-hint {
                display: flex;
                align-items: center;
                gap: 2px;
                font-size: 10px;
                color: var(--el-color-primary);
                opacity: 0.7;

                .el-icon {
                  font-size: 10px;
                }
              }
            }
          }

          .plate-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);

            .plate-status {
              display: flex;
              align-items: center;
              gap: var(--spacing-xs);
              font-size: 14px;

              .el-icon {
                &.valid {
                  color: var(--el-color-success);
                }

                &.invalid {
                  color: var(--el-color-danger);
                }
              }
            }

            .plate-type-toggle {
              display: flex;
              gap: var(--spacing-xs);
            }
          }
        }

      }

      .control-section {
        .vehicle-status {
          margin-bottom: var(--spacing-lg);

          h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
          }

          .el-radio-group {
            width: 100%;

            :deep(.el-radio-button) {
              flex: 1;

              .el-radio-button__inner {
                width: 100%;
              }
            }
          }
        }

        .main-actions {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);

          .el-button {
            height: 48px;
            font-size: 16px;
            font-weight: 600;
          }
        }

        .project-info-panel {
          margin-top: var(--spacing-lg);
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-light);
          overflow: hidden;

          .info-header {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-light);

            h4 {
              margin: 0;
              font-size: 13px;
              color: var(--text-primary);
              font-weight: 600;
            }
          }

          .info-content {
            max-height: 180px;
            overflow: hidden;
            padding: var(--spacing-md) var(--spacing-lg);
            
            .basic-info {
              margin-bottom: var(--spacing-md);
              
              .info-row {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                line-height: 1.4;
                font-size: 13px;
                
                &:last-child {
                  margin-bottom: var(--spacing-sm);
                }
                
                .info-label {
                  color: #6b7280;
                  font-weight: 600;
                  margin-right: 6px;
                  flex-shrink: 0;
                }
                
                .info-value {
                  color: #1f2937;
                  font-weight: 600;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  flex: 1;
                }
                
                .info-divider {
                  color: #d1d5db;
                  margin: 0 12px;
                  font-weight: 400;
                }
              }
            }
            
            .progress-info {
              .progress-numbers {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                font-size: 13px;
                
                .control-info {
                  color: #6b7280;
                  font-weight: 600;
                }
                
                .extracted-info {
                  color: #1f2937;
                  font-weight: 700;
                  font-family: 'Courier New', monospace;
                }
              }
              
              .progress-bar-container {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .progress-bar {
                  flex: 1;
                  height: 12px;
                  background: #f3f4f6;
                  border-radius: 6px;
                  overflow: hidden;
                  position: relative;
                  border: 1px solid #e5e7eb;
                  
                  .progress-fill {
                    height: 100%;
                    border-radius: 6px;
                    transition: width 0.8s ease-in-out;
                    position: relative;
                  }
                  
                  &.progress-normal .progress-fill {
                    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
                  }
                  
                  &.progress-warning .progress-fill {
                    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
                  }
                  
                  &.progress-danger .progress-fill {
                    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
                    animation: progress-pulse 2s infinite;
                  }
                }
                
                .progress-text {
                  font-size: 13px;
                  font-weight: 700;
                  color: #1f2937;
                  font-family: 'Courier New', monospace;
                  min-width: 40px;
                  text-align: right;
                }
              }
            }
          }
        }
        
        @keyframes progress-pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }
      }

      .recent-records {
        height: 300px; // 固定高度，正常模式
        display: flex;
        flex-direction: column;

        .records-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-md);

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        .records-list {
          flex: 1;
          overflow-y: auto;

          .record-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: var(--spacing-lg);
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-lighter);
            font-size: 13px;
            border-radius: var(--border-radius-base);
            margin-bottom: var(--spacing-xs);
            background: var(--bg-secondary);
            transition: all 0.2s ease;
            min-height: 48px;
            width: 100%;
            box-sizing: border-box;

            &:hover {
              background: var(--bg-tertiary);
              transform: translateX(2px);
            }

            &:last-child {
              border-bottom: none;
            }

            // 车牌号标签 - 第一个元素
            :deep(.el-tag) {
              min-width: 140px !important;
              max-width: 140px !important;
              text-align: center !important;
              flex-shrink: 0 !important;
              white-space: nowrap !important;
              overflow: hidden !important;
              text-overflow: ellipsis !important;
              font-weight: 600 !important;
              font-size: 13px !important;
            }

            // 重量显示 - 第二个元素
            .record-weight {
              color: var(--text-primary);
              font-weight: 700;
              font-size: 15px;
              min-width: 80px;
              max-width: 80px;
              text-align: center;
              flex-shrink: 0;
              white-space: nowrap;
            }

            // 时间显示 - 第三个元素
            .record-time {
              color: var(--text-secondary);
              font-family: 'Courier New', monospace;
              font-size: 12px;
              min-width: 60px;
              max-width: 60px;
              text-align: right;
              flex-shrink: 0;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .bottom-toolbar {
    height: 50px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-base);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);

    &.fullscreen-toolbar {
      height: 60px;
      background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
      box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
      border-top: 2px solid var(--el-color-primary);
    }

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);

      .system-title-compact {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .mini-logo {
          width: 20px;
          height: 20px;
        }

        .title-text {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-primary);
          background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .system-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        .status-details {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }

      .device-status-compact {
        display: flex;
        gap: var(--spacing-xs);
      }
    }

    .toolbar-center {
      display: flex;
      align-items: center;
      
      .time-display {
        text-align: center;
        
        .current-time-compact {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
          line-height: 1.2;
        }
        
        .current-date-compact {
          font-size: 11px;
          color: var(--text-secondary);
          margin-top: 2px;
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .fullscreen-controls {
        display: flex;
        gap: var(--spacing-xs);
        margin-right: var(--spacing-sm);
        padding-right: var(--spacing-sm);
        border-right: 1px solid var(--border-base);
      }

      .el-button {
        .el-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }
  }

  .plate-input-dialog {
    .plate-preview {
      display: flex;
      justify-content: center;
      margin-bottom: var(--spacing-md);

      .china-plate-large {
        display: inline-block;
        background: white;
        border: 4px solid #0066cc;
        border-radius: 12px;
        padding: 12px 16px;
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        transition: all 0.3s;
        transform: scale(1.2);

        &.new-energy {
          border-color: #00aa00;
        }

        .plate-header {
          &.plate-header-blue {
            background: #0066cc;
            color: white;
            text-align: center;
            font-size: 12px;
            padding: 4px 0;
            margin: -12px -16px 8px -16px;
            border-radius: 8px 8px 0 0;
            font-weight: 500;
          }

          &.plate-header-green {
            background: #00aa00;
            color: white;
            text-align: center;
            font-size: 12px;
            padding: 4px 0;
            margin: -12px -16px 8px -16px;
            border-radius: 8px 8px 0 0;
            font-weight: 500;
          }
        }

        .plate-content {
          .plate-chars {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;

            .char-box {
              width: 32px;
              height: 42px;
              background: white;
              border: 1px solid #333;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 24px;
              font-weight: bold;
              color: #333;
              font-family: 'Microsoft YaHei', 'Arial Black', Arial, sans-serif;
              text-align: center;
              line-height: 1;
              letter-spacing: 0;
            }

            .province-char {
              background: linear-gradient(135deg, #0066cc, #004499);
              color: white;
              border-color: #0066cc;
              font-family: 'Microsoft YaHei', 'SimHei', 'Arial Black', Arial, sans-serif;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
              text-align: center !important;
              vertical-align: middle;
              line-height: 1 !important;
            }

            .char-separator {
              color: #0066cc;
              font-size: 10px;
              margin: 0 3px;
              font-weight: bold;
            }

            .area-char, .number-char {
              background: #f8f8f8;
              border-color: #666;
              
              &:empty {
                background: #f0f0f0;
                border-color: #ccc;
                border-style: dashed;
                
                &::after {
                  content: '';
                  width: 14px;
                  height: 2px;
                  background: #ccc;
                }
              }
            }
          }
        }
      }
    }

    .history-section {
      .section-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-md);
      }

      .history-plates {
        margin-bottom: var(--spacing-md);
        max-height: 250px;
        overflow-y: auto;

        .history-plate-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md);
          margin-bottom: var(--spacing-xs);
          border: 1px solid var(--border-base);
          border-radius: var(--border-radius-base);
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: var(--bg-secondary);
            border-color: var(--el-color-primary);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          }

          .history-plate-info {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .plate-number-small {
              font-size: 18px;
              font-weight: bold;
              font-family: 'Arial Black', Arial, sans-serif;
              padding: 4px 8px;
              border-radius: 4px;
              color: white;

              &.blue {
                background: linear-gradient(135deg, #0066cc, #004499);
              }

              &.green {
                background: linear-gradient(135deg, #00aa00, #008800);
              }
            }

            .plate-type-small {
              font-size: 12px;
              color: var(--text-secondary);
            }
          }

          .last-used {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: right;
          }
        }
      }

      .history-actions {
        display: flex;
        justify-content: center;

        .el-button {
          min-width: 180px;
          height: 40px;
        }
      }
    }

    .plate-type-section {
      margin-bottom: var(--spacing-md);

      .section-title {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-md);
      }
    }

    .input-section {
      margin-bottom: var(--spacing-md);
      
      .section-title {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
      }

      .province-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-sm);
        margin-bottom: 0;
        
        .province-btn {
          aspect-ratio: 1;
          padding: 0;
          min-height: 40px;
          font-size: 16px;
          font-weight: bold;
          position: relative;
          font-family: 'Microsoft YaHei', 'SimHei', 'Arial Black', Arial, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          line-height: 1;
          
          &.el-button--primary {
            background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
          }

          &.hebei-highlight {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border-color: #1890ff;
            animation: hebei-glow 3s ease-in-out infinite;
            box-shadow: 0 0 12px rgba(24, 144, 255, 0.3);

            &:hover {
              background: linear-gradient(135deg, #0050b3, #1890ff);
              transform: scale(1.03);
            }
          }
        }
      }

      .area-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: var(--spacing-sm);
        margin-bottom: 0;
        
        .area-btn {
          aspect-ratio: 1;
          padding: 0;
          min-height: 36px;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .number-grid {
        display: grid;
        grid-template-columns: repeat(9, 1fr);
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        
        .number-btn {
          aspect-ratio: 1;
          padding: 0;
          min-height: 36px;
          font-size: 14px;
          font-weight: 600;
          
          &:disabled {
            opacity: 0.3;
          }
        }
      }

      .dialog-control-buttons {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: center;
        
        .el-button {
          min-width: 100px;
          height: 40px;
          font-weight: 600;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: var(--spacing-md);

      .el-button {
        min-width: 120px;
        height: 40px;
        font-weight: 600;
      }
    }
  }

}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes hebei-glow {
  0% { 
    box-shadow: 0 0 12px rgba(24, 144, 255, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.5);
    transform: scale(1.01);
  }
  100% { 
    box-shadow: 0 0 12px rgba(24, 144, 255, 0.3);
    transform: scale(1);
  }
}
</style>
