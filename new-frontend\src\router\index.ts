import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ showSpinner: false })

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Boot',
    component: () => import('@/views/Boot/index.vue'),
    meta: {
      title: '系统启动'
    }
  },
  {
    path: '/old-boot',
    name: 'SystemBoot',
    component: () => import('@/views/SystemBoot/index.vue'),
    meta: {
      title: '一体机系统启动（旧版）'
    }
  },
  {
    path: '/terminal',
    name: 'Terminal',
    component: () => import('@/views/Terminal/DualCameraTerminal.vue'),
    meta: {
      title: '河北省采(弃)砂监管一体机系统'
    }
  },
  {
    path: '/watermark-test',
    name: 'WatermarkTest',
    component: () => import('@/views/WatermarkTest.vue'),
    meta: {
      title: '水印测试页面'
    }
  },
  {
    path: '/loading',
    name: 'Loading',
    component: () => import('@/views/Loading/index.vue'),
    meta: {
      title: '系统启动中'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta?.title as string
  if (title) {
    document.title = `${title} - 河北省采(弃)砂监管一体机系统`
  }

  // 这里可以添加权限验证逻辑
  // 暂时先直接通过
  next()
})

// 全局后置守卫
router.afterEach(() => {
  NProgress.done()
})

export default router