<template>
  <el-card class="plate-input-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><EditPen /></el-icon>
          <span class="header-title">车牌号录入</span>
        </div>
        <div class="header-right">
          <el-tag :type="plateStatusType" size="small">
            {{ plateStatusText }}
          </el-tag>
        </div>
      </div>
    </template>

    <div class="plate-content">
      <!-- 车牌显示区 -->
      <div class="plate-display">
        <div class="plate-preview" :class="plateValidClass">
          <span class="province">{{ plateData.province || '省' }}</span>
          <span class="numbers">{{ plateData.numbers || 'A12345' }}</span>
        </div>
        <div class="plate-info" v-if="plateNumber">
          <el-icon class="info-icon" :class="plateValidClass">
            <component :is="plateValid ? 'SuccessFilled' : 'WarningFilled'" />
          </el-icon>
          <span class="info-text">{{ plateInfoText }}</span>
        </div>
      </div>

      <!-- 输入方式切换 -->
      <div class="input-methods">
        <el-tabs v-model="inputMode" @tab-change="handleModeChange">
          <el-tab-pane label="自动识别" name="auto">
            <div class="auto-recognition">
              <div class="recognition-status">
                <el-icon class="status-icon" :class="recognitionStatusClass">
                  <component :is="recognitionIcon" />
                </el-icon>
                <p class="status-text">{{ recognitionStatusText }}</p>
              </div>
              <div class="recognition-actions">
                <el-button 
                  type="primary" 
                  @click="triggerRecognition"
                  :loading="recognitionLoading"
                >
                  手动识别
                </el-button>
                <el-button @click="clearPlate">清除</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="手动输入" name="manual">
            <div class="manual-input">
              <!-- 省份选择 -->
              <div class="province-selector">
                <el-select 
                  v-model="plateData.province" 
                  placeholder="省份"
                  style="width: 80px"
                  size="large"
                >
                  <el-option 
                    v-for="province in provinces"
                    :key="province"
                    :label="province"
                    :value="province"
                  />
                </el-select>
              </div>
              
              <!-- 号码输入 -->
              <div class="number-input">
                <el-input
                  v-model="plateData.numbers"
                  placeholder="A12345"
                  maxlength="6"
                  size="large"
                  style="width: 120px"
                  @input="handleNumberInput"
                />
              </div>
              
              <!-- 虚拟键盘 -->
              <div class="virtual-keyboard">
                <div class="keyboard-row">
                  <el-button 
                    v-for="char in keyboardChars.letters"
                    :key="char"
                    size="small"
                    @click="inputChar(char)"
                  >
                    {{ char }}
                  </el-button>
                </div>
                <div class="keyboard-row">
                  <el-button 
                    v-for="char in keyboardChars.numbers"
                    :key="char"
                    size="small"
                    @click="inputChar(char)"
                  >
                    {{ char }}
                  </el-button>
                </div>
                <div class="keyboard-row">
                  <el-button size="small" type="warning" @click="deleteLast">
                    删除
                  </el-button>
                  <el-button size="small" type="info" @click="clearPlate">
                    清空
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 确认操作 -->
      <div class="confirm-actions">
        <el-button 
          type="primary" 
          size="large"
          @click="confirmPlate"
          :disabled="!plateNumber || !plateValid"
          style="width: 100%"
        >
          确认车牌
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 车牌输入卡片组件
// 作者：仕伟

import { vehicleApi } from '@/services/api'
import { useWebSocket } from '@/services/websocket'

// 车牌数据
const plateData = reactive({
  province: '',
  numbers: ''
})

// 输入模式
const inputMode = ref('auto')

// 识别状态
const recognitionLoading = ref(false)
const plateValid = ref(false)
const platePermitInfo = ref<any>(null)

// WebSocket连接
const { subscribe } = useWebSocket()

// 省份列表
const provinces = [
  '京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘',
  '皖', '鲁', '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋',
  '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏', '川', '宁', '琼'
]

// 虚拟键盘字符
const keyboardChars = {
  letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
  numbers: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
}

// 计算属性
const plateNumber = computed(() => {
  if (plateData.province && plateData.numbers) {
    return `${plateData.province}${plateData.numbers}`
  }
  return ''
})

const plateStatusType = computed(() => {
  if (!plateNumber.value) return 'info'
  return plateValid.value ? 'success' : 'danger'
})

const plateStatusText = computed(() => {
  if (!plateNumber.value) return '未输入'
  return plateValid.value ? '有效' : '无效'
})

const plateValidClass = computed(() => {
  if (!plateNumber.value) return ''
  return plateValid.value ? 'valid' : 'invalid'
})

const plateInfoText = computed(() => {
  if (!plateNumber.value) return ''
  if (plateValid.value && platePermitInfo.value) {
    return `有运砂许可，允许载重${platePermitInfo.value.allowedWeight}吨`
  }
  return plateValid.value ? '车牌有效' : '车牌无效或无许可'
})

const recognitionStatusClass = computed(() => {
  return recognitionLoading.value ? 'loading' : plateValid.value ? 'success' : 'waiting'
})

const recognitionIcon = computed(() => {
  if (recognitionLoading.value) return 'Loading'
  if (plateNumber.value && plateValid.value) return 'SuccessFilled'
  if (plateNumber.value && !plateValid.value) return 'WarningFilled'
  return 'Camera'
})

const recognitionStatusText = computed(() => {
  if (recognitionLoading.value) return '正在识别中...'
  if (inputMode.value === 'auto') {
    if (plateNumber.value) {
      return plateValid.value ? '识别成功' : '识别失败'
    }
    return '等待车辆进入识别区域'
  }
  return '手动输入模式'
})

// 方法
const handleModeChange = (mode: string) => {
  console.log('切换输入模式:', mode)
  if (mode === 'auto') {
    // 切换到自动识别模式
    startAutoRecognition()
  } else {
    // 切换到手动输入模式
    stopAutoRecognition()
  }
}

const triggerRecognition = async () => {
  recognitionLoading.value = true
  try {
    // TODO: 触发手动抓拍识别
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟识别结果
    const mockResult = {
      plateNumber: '冀A12345',
      confidence: 0.95
    }
    
    const [province, ...numbers] = mockResult.plateNumber.split('')
    plateData.province = province
    plateData.numbers = numbers.join('')
    
    // 验证车牌
    await validatePlate(mockResult.plateNumber)
    
    ElMessage.success('车牌识别成功')
  } catch (error) {
    ElMessage.error('识别失败，请重试')
  } finally {
    recognitionLoading.value = false
  }
}

const inputChar = (char: string) => {
  if (plateData.numbers.length < 6) {
    plateData.numbers += char
    
    // 实时验证 - 支持7位民用车牌和8位新能源车牌
    const length = plateNumber.value.length
    if (length >= 7 && length <= 8) {
      validatePlate(plateNumber.value)
    }
  }
}

const deleteLast = () => {
  if (plateData.numbers.length > 0) {
    plateData.numbers = plateData.numbers.slice(0, -1)
  }
}

const clearPlate = () => {
  plateData.province = ''
  plateData.numbers = ''
  plateValid.value = false
  platePermitInfo.value = null
}

const handleNumberInput = (value: string) => {
  // 实时验证 - 支持7位民用车牌和8位新能源车牌
  const length = plateNumber.value.length
  if (length >= 7 && length <= 8) {
    validatePlate(plateNumber.value)
  }
}

const validatePlate = async (plate: string) => {
  try {
    const result = await vehicleApi.validatePlateNumber(plate)
    plateValid.value = result.valid
    platePermitInfo.value = result.permitInfo
    
    if (!result.valid) {
      ElMessage.warning('该车牌无运砂许可或已失效')
    }
  } catch (error) {
    console.error('验证车牌失败:', error)
    plateValid.value = false
    platePermitInfo.value = null
  }
}

const confirmPlate = () => {
  if (!plateNumber.value || !plateValid.value) {
    ElMessage.warning('请输入有效的车牌号')
    return
  }
  
  ElMessage.success(`已确认车牌：${plateNumber.value}`)
  
  // TODO: 触发确认车牌的业务逻辑
  console.log('确认车牌:', plateNumber.value, platePermitInfo.value)
  
  // 发送确认事件给父组件
  // emit('plate-confirmed', { plateNumber: plateNumber.value, permitInfo: platePermitInfo.value })
}

const startAutoRecognition = () => {
  console.log('开始自动识别模式')
  // TODO: 启动自动识别
}

const stopAutoRecognition = () => {
  console.log('停止自动识别模式')
  // TODO: 停止自动识别
}

// 生命周期
onMounted(() => {
  // 订阅车牌识别结果
  subscribe('PLATE_RECOGNITION', (data) => {
    if (inputMode.value === 'auto') {
      const [province, ...numbers] = data.plateNumber.split('')
      plateData.province = province
      plateData.numbers = numbers.join('')
      
      // 自动验证识别的车牌
      validatePlate(data.plateNumber)
      
      ElMessage.success(`自动识别到车牌：${data.plateNumber}`)
    }
  })
  
  // 默认启动自动识别
  if (inputMode.value === 'auto') {
    startAutoRecognition()
  }
})

onUnmounted(() => {
  stopAutoRecognition()
})
</script>

<style scoped lang="scss">
.plate-input-card {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }
  }

  .plate-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .plate-display {
      text-align: center;
      padding: var(--spacing-lg) 0;
      border-bottom: 1px solid var(--border-base);

      .plate-preview {
        display: inline-flex;
        align-items: center;
        padding: var(--spacing-sm) var(--spacing-md);
        border: 2px solid var(--border-base);
        border-radius: var(--border-radius-base);
        background: linear-gradient(135deg, #1e3a5f, #2d4a6b);
        color: white;
        font-family: 'Arial Black', sans-serif;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: var(--spacing-md);
        min-width: 180px;
        justify-content: center;
        transition: var(--transition-base);

        &.valid {
          border-color: var(--color-plate-valid);
          box-shadow: 0 0 10px rgba(82, 196, 26, 0.3);
        }

        &.invalid {
          border-color: var(--color-plate-invalid);
          box-shadow: 0 0 10px rgba(255, 77, 79, 0.3);
        }

        .province {
          background: #1a5490;
          padding: 2px 6px;
          border-radius: 2px;
          margin-right: 4px;
          min-width: 24px;
          text-align: center;
        }

        .numbers {
          letter-spacing: 2px;
        }
      }

      .plate-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        font-size: 14px;

        .info-icon {
          font-size: 16px;

          &.valid {
            color: var(--color-plate-valid);
          }

          &.invalid {
            color: var(--color-plate-invalid);
          }
        }

        .info-text {
          color: var(--text-regular);
        }
      }
    }

    .input-methods {
      flex: 1;

      .auto-recognition {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg) 0;

        .recognition-status {
          text-align: center;

          .status-icon {
            font-size: 48px;
            margin-bottom: var(--spacing-md);

            &.loading {
              color: var(--el-color-warning);
              animation: spin 1s linear infinite;
            }

            &.success {
              color: var(--color-device-online);
            }

            &.waiting {
              color: var(--text-placeholder);
            }
          }

          .status-text {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
          }
        }

        .recognition-actions {
          display: flex;
          gap: var(--spacing-sm);
        }
      }

      .manual-input {
        .province-selector,
        .number-input {
          margin-bottom: var(--spacing-md);
          text-align: center;
        }

        .virtual-keyboard {
          .keyboard-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            justify-content: center;
            margin-bottom: var(--spacing-sm);

            .el-button {
              min-width: 32px;
              padding: var(--spacing-xs);
            }
          }
        }
      }
    }

    .confirm-actions {
      margin-top: auto;
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--border-base);
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>