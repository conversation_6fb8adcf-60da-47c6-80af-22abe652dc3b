import { defineStore } from 'pinia'
import { deviceApi } from '@/services/api'
import { ElMessage } from 'element-plus'

// 设备状态管理 Store
// 作者：仕伟

export type DeviceStatus = 'online' | 'offline' | 'warning' | 'error'

interface DeviceInfo {
  status: DeviceStatus
  lastUpdate: number
  message?: string
}

interface DeviceState {
  status: {
    camera: DeviceStatus
    scale: DeviceStatus
    gate: DeviceStatus
  }
  info: {
    camera: DeviceInfo
    scale: DeviceInfo  
    gate: DeviceInfo
  }
}

export const useDeviceStore = defineStore('device', {
  state: (): DeviceState => ({
    status: {
      camera: 'online',
      scale: 'online', 
      gate: 'online'
    },
    info: {
      camera: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '摄像头工作正常'
      },
      scale: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '地磅连接正常'
      },
      gate: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '道闸控制正常'
      }
    }
  }),

  getters: {
    // 获取所有设备的整体状态
    overallStatus(): DeviceStatus {
      const statuses = Object.values(this.status)
      
      if (statuses.includes('error')) return 'error'
      if (statuses.includes('offline')) return 'offline'  
      if (statuses.includes('warning')) return 'warning'
      return 'online'
    },

    // 获取在线设备数量
    onlineCount(): number {
      return Object.values(this.status).filter(status => status === 'online').length
    },

    // 获取离线设备数量  
    offlineCount(): number {
      return Object.values(this.status).filter(status => status === 'offline').length
    },

    // 设备总数
    totalCount(): number {
      return Object.keys(this.status).length
    }
  },

  actions: {
    // 更新单个设备状态
    updateDeviceStatus(device: keyof DeviceState['status'], status: DeviceStatus, message?: string) {
      this.status[device] = status
      this.info[device] = {
        status,
        lastUpdate: Date.now(),
        message: message || this.info[device].message
      }
    },

    // 更新摄像头状态
    updateCameraStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('camera', status, message)
    },

    // 更新地磅状态
    updateScaleStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('scale', status, message)
    },

    // 更新道闸状态
    updateGateStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('gate', status, message)
    },

    // 批量更新设备状态
    updateAllDeviceStatus(statusMap: Partial<DeviceState['status']>) {
      Object.entries(statusMap).forEach(([device, status]) => {
        if (status) {
          this.updateDeviceStatus(device as keyof DeviceState['status'], status)
        }
      })
    },

    // 重置所有设备状态为在线
    resetAllDeviceStatus() {
      this.updateAllDeviceStatus({
        camera: 'online',
        scale: 'online',
        gate: 'online'
      })
    },

    // 从真实API获取设备状态
    async fetchDeviceStatus() {
      try {
        console.log('🔄 获取设备状态...')
        const response = await deviceApi.getDeviceStatus()
        
        // 更新设备状态
        this.updateDeviceStatus('camera', response.camera, '摄像头状态更新')
        this.updateDeviceStatus('scale', response.scale, '地磅状态更新') 
        this.updateDeviceStatus('gate', response.gate, '道闸状态更新')
        
        console.log('✅ 设备状态更新成功:', response)
        return response
      } catch (error: any) {
        console.error('❌ 获取设备状态失败:', error)
        
        // 网络错误时，将所有设备状态设为错误
        this.updateAllDeviceStatus({
          camera: 'error',
          scale: 'error', 
          gate: 'error'
        })
        
        // 不显示错误消息，避免过于频繁的提示
        throw error
      }
    },

    // 刷新特定设备状态
    async refreshDeviceStatus(deviceType: 'camera' | 'scale' | 'gate') {
      try {
        // 先更新为获取中状态
        this.updateDeviceStatus(deviceType, 'warning', '状态检查中...')
        
        // 调用真实API获取最新状态
        await this.fetchDeviceStatus()
      } catch (error) {
        this.updateDeviceStatus(deviceType, 'error', '状态获取失败')
      }
    },

    // 检查设备连接超时
    checkDeviceTimeout() {
      const timeout = 30000 // 30秒超时
      const now = Date.now()
      
      Object.entries(this.info).forEach(([device, info]) => {
        if (now - info.lastUpdate > timeout && info.status === 'online') {
          this.updateDeviceStatus(
            device as keyof DeviceState['status'], 
            'offline', 
            '设备连接超时'
          )
        }
      })
    },

    // 初始化设备状态监控 - 使用真实API
    async initDeviceMonitoring() {
      console.log('🚀 初始化设备状态监控...')
      
      // 首次获取设备状态
      try {
        await this.fetchDeviceStatus()
      } catch (error) {
        console.error('初始设备状态获取失败')
      }
      
      // 定期轮询设备状态（每30秒）
      const statusInterval = setInterval(async () => {
        try {
          await this.fetchDeviceStatus()
        } catch (error) {
          console.error('定期设备状态更新失败')
        }
      }, 30000)
      
      // 定期检查设备连接超时（每10秒）
      const timeoutInterval = setInterval(() => {
        this.checkDeviceTimeout()
      }, 10000)
      
      console.log('✅ 设备状态监控已启动')
      
      // 返回清理函数
      return () => {
        clearInterval(statusInterval)
        clearInterval(timeoutInterval)
        console.log('🧹 设备状态监控已停止')
      }
    },

    // 手动触发设备控制操作
    async controlDevice(deviceType: 'gate', action: string) {
      try {
        console.log(`🎮 控制设备: ${deviceType} - ${action}`)
        
        let result
        switch (deviceType) {
          case 'gate':
            if (action === 'trigger') {
              result = await deviceApi.triggerGate()
            }
            break
          default:
            throw new Error(`不支持的设备类型: ${deviceType}`)
        }
        
        if (result?.success) {
          ElMessage.success(result.message || '设备控制成功')
          // 控制成功后刷新设备状态
          await this.refreshDeviceStatus(deviceType)
        } else {
          throw new Error(result?.message || '设备控制失败')
        }
        
        return result
      } catch (error: any) {
        console.error(`❌ 设备控制失败:`, error)
        ElMessage.error(error.message || '设备控制失败')
        throw error
      }
    }
  }
})