/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_MQTT_URL: string
  readonly VITE_MQTT_USER: string
  readonly VITE_MQTT_PASS: string
  readonly VITE_ENCRYPT_KEY: string
  readonly VITE_ENCRYPT_IV: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}