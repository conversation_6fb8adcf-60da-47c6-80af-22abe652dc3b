/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    FullScreenWatermark: typeof import('./src/components/FullScreenWatermark.vue')['default']
    HistoryDialog: typeof import('./src/components/HistoryDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingsDialog: typeof import('./src/components/SettingsDialog.vue')['default']
    StatusIndicator: typeof import('./src/components/StatusIndicator.vue')['default']
    ThemeToggle: typeof import('./src/components/ThemeToggle.vue')['default']
    Watermark: typeof import('./src/components/Watermark.vue')['default']
    WeightWebSocketStatus: typeof import('./src/components/WeightWebSocketStatus.vue')['default']
  }
}
