<template>
  <el-card class="recent-records" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">最近记录</span>
          <el-tag size="small" type="info">{{ records.length }}</el-tag>
        </div>
        <div class="header-right">
          <el-button size="small" @click="refreshRecords" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" type="primary" @click="viewAllRecords">
            <el-icon><More /></el-icon>
            查看全部
          </el-button>
        </div>
      </div>
    </template>

    <div class="table-container">
      <el-table 
        :data="records" 
        v-loading="loading"
        :size="tableSize"
        style="width: 100%"
        :max-height="tableHeight"
        :row-class-name="getRowClassName"
        stripe
        highlight-current-row
        :empty-text="'暂无数据'"
      >
        <el-table-column prop="plateNumber" label="车牌号" :width="plateNumberWidth" align="center">
          <template #default="{ row }">
            <div class="plate-number-cell">
              <el-tag 
                size="small" 
                :type="getPlateTagType(row.plateNumber)" 
                effect="dark"
                class="plate-tag"
              >
                {{ row.plateNumber }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="weight" label="重量" :width="weightWidth" align="center">
          <template #default="{ row }">
            <div class="weight-cell">
              <span :class="getWeightClass(row.weight)" class="weight-value">
                {{ formatWeight(row.weight) }}
              </span>
              <span class="weight-unit">吨</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" :width="statusWidth" align="center">
          <template #default="{ row }">
            <div class="status-cell">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                size="small"
                effect="dark"
                class="status-tag"
              >
                <el-icon class="status-icon">
                  <ArrowRight v-if="row.status === 'IN'" />
                  <ArrowLeft v-else />
                </el-icon>
                {{ row.status === 'IN' ? '入场' : '出场' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="timestamp" label="时间" align="center" :min-width="timeMinWidth">
          <template #default="{ row }">
            <div class="time-display">
              <div class="time-main">
                <el-icon class="time-icon"><Clock /></el-icon>
                {{ formatSmartTime(row.timestamp) }}
              </div>
              <div class="time-relative">{{ getRelativeTime(row.timestamp) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" :width="actionWidth" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button 
                  size="small" 
                  type="primary" 
                  :icon="View" 
                  circle 
                  @click="viewRecord(row)"
                  class="action-btn"
                />
              </el-tooltip>
              <el-dropdown @command="(cmd) => handleCommand(cmd, row)" trigger="hover">
                <el-button 
                  size="small" 
                  type="info" 
                  :icon="MoreFilled" 
                  circle 
                  class="action-btn"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="images">
                      <el-icon><Picture /></el-icon>查看照片
                    </el-dropdown-item>
                    <el-dropdown-item command="copy" divided>
                      <el-icon><CopyDocument /></el-icon>复制车牌
                    </el-dropdown-item>
                    <el-dropdown-item command="export">
                      <el-icon><Download /></el-icon>导出记录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && records.length === 0" class="empty-state">
        <el-empty description="暂无记录" :image-size="80">
          <el-button type="primary" @click="refreshRecords">刷新数据</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-item">
        <span class="stat-label">今日入场:</span>
        <span class="stat-value stat-in">{{ todayInCount }}辆</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日出场:</span>
        <span class="stat-value stat-out">{{ todayOutCount }}辆</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总重量:</span>
        <span class="stat-value">{{ totalWeight }}吨</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 最近记录表格组件
// 作者：仕伟

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { vehicleApi, type VehicleRecord } from '@/services/api'
import { useWebSocket } from '@/services/websocket'
import { ArrowRight, ArrowLeft, Clock, View, MoreFilled, Picture, CopyDocument, Download } from '@element-plus/icons-vue'
import { formatSmartTime, getRelativeTime, isToday } from '@/utils/timeFormatter'
import dayjs from 'dayjs'

const router = useRouter()

// 组件状态
const records = ref<VehicleRecord[]>([])
const loading = ref(false)

// WebSocket连接
const { subscribe } = useWebSocket()

// 计算统计数据
const todayInCount = computed(() => {
  return records.value.filter(r => 
    r.status === 'IN' && isToday(r.timestamp)
  ).length
})

const todayOutCount = computed(() => {
  return records.value.filter(r => 
    r.status === 'OUT' && isToday(r.timestamp)
  ).length
})

const totalWeight = computed(() => {
  const total = records.value.reduce((sum, record) => sum + record.weight, 0)
  return total.toFixed(1)
})

// 获取表格行样式类名
const getRowClassName = ({ row }: { row: VehicleRecord }) => {
  const age = Date.now() - row.timestamp
  if (age < 5 * 60 * 1000) return 'row-new' // 5分钟内的新记录
  if (row.weight > 40) return 'row-overload' // 超重记录
  return ''
}

// 获取车牌标签类型
const getPlateTagType = (plateNumber: string) => {
  // 根据车牌号特征返回不同颜色
  if (plateNumber.includes('京')) return 'primary'
  if (plateNumber.includes('沪')) return 'success'
  if (plateNumber.includes('粤')) return 'warning'
  return 'info'
}

// 获取重量样式类
const getWeightClass = (weight: number) => {
  if (weight > 40) return 'weight-overload'
  if (weight > 30) return 'weight-warning'
  if (weight < 5) return 'weight-light'
  return 'weight-normal'
}

// 格式化重量显示
const formatWeight = (weight: number) => {
  return weight.toFixed(1)
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  return status === 'IN' ? 'success' : 'warning'
}

// 响应式计算属性
const tableSize = computed(() => {
  if (window.innerWidth >= 1600) return 'default' // 超大屏
  if (window.innerWidth >= 1200) return 'small'   // 大屏  
  return 'small' // 中小屏
})

const tableHeight = computed(() => {
  if (window.innerWidth >= 1600) return 320      // 超大屏
  if (window.innerWidth >= 1200) return 280      // 大屏
  if (window.innerWidth >= 992) return 260       // 中大屏
  return 240 // 中小屏
})

const plateNumberWidth = computed(() => {
  if (window.innerWidth >= 1600) return 130      // 超大屏
  if (window.innerWidth >= 1200) return 120      // 大屏
  return 110 // 中小屏
})

const weightWidth = computed(() => {
  if (window.innerWidth >= 1600) return 110      // 超大屏
  if (window.innerWidth >= 1200) return 100      // 大屏
  return 90  // 中小屏
})

const statusWidth = computed(() => {
  if (window.innerWidth >= 1600) return 90       // 超大屏
  if (window.innerWidth >= 1200) return 85       // 大屏
  return 75  // 中小屏
})

const timeMinWidth = computed(() => {
  if (window.innerWidth >= 1600) return 180      // 超大屏，增加宽度以容纳完整时间
  if (window.innerWidth >= 1200) return 170      // 大屏
  return 160 // 中小屏
})

const actionWidth = computed(() => {
  if (window.innerWidth >= 1600) return 110      // 超大屏
  if (window.innerWidth >= 1200) return 100      // 大屏
  return 90  // 中小屏
})

// 使用统一的时间格式化工具

// 刷新记录
const refreshRecords = async () => {
  loading.value = true
  
  try {
    // 获取最近20条记录
    const result = await vehicleApi.queryVehicleHistory({
      page: 1,
      limit: 20
    })
    
    const sortedRecords = result.sort((a, b) => b.timestamp - a.timestamp)
    
    // 检查是否有新记录
    if (records.value.length > 0 && sortedRecords.length > 0) {
      const latestTimestamp = records.value[0]?.timestamp || 0
      const newRecords = sortedRecords.filter(r => r.timestamp > latestTimestamp)
      
      if (newRecords.length > 0) {
        ElMessage.success(`发现${newRecords.length}条新记录`)
      }
    }
    
    records.value = sortedRecords
  } catch (error) {
    ElMessage.error('获取记录失败，请检查网络连接')
    console.error('获取记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看全部记录
const viewAllRecords = () => {
  router.push('/history')
}

// 查看记录详情
const viewRecord = (record: VehicleRecord) => {
  ElMessage.info(`查看${record.plateNumber}详情`)
  // TODO: 打开详情对话框
}

// 处理下拉菜单命令
const handleCommand = (command: string, record: VehicleRecord) => {
  switch (command) {
    case 'images':
      if (record.images && record.images.length > 0) {
        ElMessage.info(`查看${record.plateNumber}的${record.images.length}张照片`)
        // TODO: 打开图片预览
      } else {
        ElMessage.warning('该记录暂无照片')
      }
      break
      
    case 'copy':
      // 复制车牌号到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(record.plateNumber).then(() => {
          ElMessage.success(`已复制车牌号: ${record.plateNumber}`)
        })
      } else {
        ElMessage.warning('浏览器不支持复制功能')
      }
      break
      
    case 'export':
      ElMessage.info(`导出${record.plateNumber}的记录数据`)
      // TODO: 实现单条记录导出
      break
  }
}

// 添加新记录到列表顶部
const addNewRecord = (record: VehicleRecord) => {
  records.value.unshift(record)
  
  // 根据屏幕尺寸调整记录数量
  const maxRecords = window.innerWidth >= 1600 ? 25 : 20
  if (records.value.length > maxRecords) {
    records.value = records.value.slice(0, maxRecords)
  }
  
  // 显示通知
  const statusText = record.status === 'IN' ? '入场' : '出场'
  const weightText = record.weight > 0 ? `，重量${record.weight}吨` : ''
  ElNotification({
    title: '新增记录',
    message: `${record.plateNumber} ${statusText}${weightText}`,
    type: record.weight > 40 ? 'warning' : 'success',
    duration: 4000,
    position: 'top-right'
  })
}

// 窗口尺寸变化监听
let resizeTimer: NodeJS.Timeout
const handleResize = () => {
  clearTimeout(resizeTimer)
  resizeTimer = setTimeout(() => {
    // 触发响应式重新计算
    nextTick(() => {
      // 强制重新渲染表格
    })
  }, 150)
}

// 页面挂载时获取数据
onMounted(() => {
  refreshRecords()
  
  // 订阅实时数据更新
  subscribe('VEHICLE_RECORD', (data) => {
    // 当有新的车辆记录时，添加到列表
    addNewRecord(data)
  })
  
  // 添加窗口尺寸监听
  window.addEventListener('resize', handleResize)
  
  // 定期刷新数据
  const timer = setInterval(() => {
    refreshRecords()
  }, 60000) // 每分钟刷新一次
  
  onUnmounted(() => {
    clearInterval(timer)
    window.removeEventListener('resize', handleResize)
    clearTimeout(resizeTimer)
  })
})
</script>

<style scoped lang="scss">
.recent-records {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 var(--spacing-md);

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .header-right {
      .el-button {
        margin-left: var(--spacing-xs);

        .el-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }
  }

  .table-container {
    flex: 1;
    padding: 0 var(--spacing-md);
    overflow: hidden;

    :deep(.el-table) {
      /* 表格行样式 */
      .row-new {
        background-color: var(--el-color-success-light-9) !important;
        
        .plate-tag {
          animation: pulse 2s infinite;
        }
      }
      
      .row-overload {
        background-color: var(--el-color-warning-light-9) !important;
      }
      
      /* 车牌号单元格 */
      .plate-number-cell {
        display: flex;
        justify-content: center;
        align-items: center;
        
        .plate-tag {
          font-family: 'Arial', sans-serif;
          font-weight: 700;
          letter-spacing: 1px;
          border-radius: 6px;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }
      
      /* 重量单元格 */
      .weight-cell {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
        
        .weight-value {
          font-family: 'Courier New', monospace;
          font-weight: 600;
          font-size: 14px;
          
          &.weight-normal {
            color: var(--text-primary);
          }
          
          &.weight-warning {
            color: var(--el-color-warning);
          }
          
          &.weight-overload {
            color: var(--color-weight-overload);
            animation: blink 1.5s infinite;
          }
          
          &.weight-light {
            color: var(--text-secondary);
          }
        }
        
        .weight-unit {
          font-size: 10px;
          color: var(--text-placeholder);
        }
      }
      
      /* 状态单元格 */
      .status-cell {
        display: flex;
        justify-content: center;
        align-items: center;
        
        .status-tag {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 12px;
          transition: all 0.3s ease;
          
          .status-icon {
            font-size: 12px;
          }
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
          }
        }
      }
      
      /* 时间单元格 */
      .time-display {
        .time-main {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
          font-size: 13px;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
          font-weight: 500;
          line-height: 1.4;
          
          .time-icon {
            font-size: 14px;
            color: var(--el-color-primary);
            flex-shrink: 0;
          }
        }

        .time-relative {
          font-size: 10px;
          color: var(--text-placeholder);
          margin-top: 3px;
          text-align: center;
          font-style: italic;
        }
      }
      
      /* 操作按钮 */
      .action-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        
        .action-btn {
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }

    .empty-state {
      padding: var(--spacing-xl) 0;
      text-align: center;
      
      :deep(.el-empty) {
        .el-empty__description {
          color: var(--text-secondary);
          margin-bottom: var(--spacing-md);
        }
      }
    }
  }

  .quick-stats {
    border-top: 1px solid var(--border-base);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success), var(--el-color-warning));
    }

    .stat-item {
      text-align: center;
      flex: 1;
      position: relative;
      padding: var(--spacing-sm);
      transition: all 0.3s ease;
      border-radius: 8px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);
      }
      
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        bottom: 20%;
        width: 1px;
        background: var(--border-light);
      }

      .stat-label {
        display: block;
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
        font-weight: 500;
      }

      .stat-value {
        display: block;
        font-size: 18px;
        font-weight: 700;
        color: var(--text-primary);
        font-family: 'Courier New', monospace;
        transition: all 0.3s ease;

        &.stat-in {
          color: var(--el-color-success);
          text-shadow: 0 0 10px rgba(103, 194, 58, 0.3);
        }

        &.stat-out {
          color: var(--el-color-warning);
          text-shadow: 0 0 10px rgba(230, 162, 60, 0.3);
        }
      }
    }
  }
}

/* 电脑端响应式调整 */
@media (min-width: 1600px) {
  .recent-records {
    :deep(.el-table) {
      font-size: 14px;
      
      .el-table__cell {
        padding: 10px 0;
      }
      
      .plate-tag, .status-tag {
        font-size: 12px;
        padding: 5px 10px;
      }
      
      .weight-value {
        font-size: 16px;
      }
      
      .time-main {
        font-size: 14px;
      }
      
      .time-relative {
        font-size: 11px;
      }
    }
    
    .quick-stats {
      padding: var(--spacing-lg);
      
      .stat-value {
        font-size: 20px;
      }
    }
  }
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .recent-records {
    :deep(.el-table) {
      font-size: 13px;
      
      .plate-tag, .status-tag {
        font-size: 11px;
      }
      
      .weight-value {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 1199px) {
  .recent-records {
    :deep(.el-table) {
      font-size: 12px;
      
      .el-table__cell {
        padding: 8px 0;
      }
      
      .plate-tag, .status-tag {
        font-size: 10px;
        padding: 3px 6px;
      }
      
      .weight-value {
        font-size: 13px;
      }
      
      .action-buttons {
        gap: 4px;
        
        .action-btn {
          width: 24px;
          height: 24px;
        }
      }
    }
    
    .quick-stats {
      .stat-value {
        font-size: 16px;
      }
      
      .stat-label {
        font-size: 11px;
      }
    }
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.7;
  }
}
</style>