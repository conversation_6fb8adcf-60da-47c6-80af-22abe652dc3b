/**
 * 时间格式化工具
 * 作者：仕伟
 */

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 智能时间格式化 - 根据时间距离现在的长度自动选择格式
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
export const formatSmartTime = (timestamp: number): string => {
  const now = dayjs()
  const time = dayjs(timestamp)
  
  // 如果是今天，显示 今天 HH:mm:ss
  if (time.isSame(now, 'day')) {
    return `今天 ${time.format('HH:mm:ss')}`
  }
  
  // 如果是昨天，显示 昨天 HH:mm:ss
  if (time.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${time.format('HH:mm:ss')}`
  }
  
  // 如果是今年，显示 MM-DD HH:mm:ss
  if (time.isSame(now, 'year')) {
    return time.format('MM-DD HH:mm:ss')
  }
  
  // 其他情况显示完整年月日时间
  return time.format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化完整时间 - 始终显示年月日时分秒
 * @param timestamp 时间戳
 * @returns YYYY-MM-DD HH:mm:ss 格式的时间
 */
export const formatFullTime = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化日期 - 只显示年月日
 * @param timestamp 时间戳
 * @returns YYYY-MM-DD 格式的日期
 */
export const formatDate = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

/**
 * 格式化时间 - 只显示时分秒
 * @param timestamp 时间戳
 * @returns HH:mm:ss 格式的时间
 */
export const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('HH:mm:ss')
}

/**
 * 格式化简短时间 - 只显示时分
 * @param timestamp 时间戳
 * @returns HH:mm 格式的时间
 */
export const formatShortTime = (timestamp: number): string => {
  return dayjs(timestamp).format('HH:mm')
}

/**
 * 格式化月日时间 - 显示月日时分秒
 * @param timestamp 时间戳
 * @returns MM-DD HH:mm:ss 格式的时间
 */
export const formatMonthDayTime = (timestamp: number): string => {
  return dayjs(timestamp).format('MM-DD HH:mm:ss')
}

/**
 * 获取相对时间 - 几分钟前，几小时前等
 * @param timestamp 时间戳
 * @returns 相对时间描述
 */
export const getRelativeTime = (timestamp: number): string => {
  return dayjs(timestamp).fromNow()
}

/**
 * 获取时间差 - 到现在的时间差
 * @param timestamp 时间戳
 * @returns 到现在的时间差对象
 */
export const getTimeDiff = (timestamp: number) => {
  const now = dayjs()
  const time = dayjs(timestamp)
  
  return {
    years: now.diff(time, 'year'),
    months: now.diff(time, 'month'),
    days: now.diff(time, 'day'),
    hours: now.diff(time, 'hour'),
    minutes: now.diff(time, 'minute'),
    seconds: now.diff(time, 'second')
  }
}

/**
 * 判断是否为今天
 * @param timestamp 时间戳
 * @returns 是否为今天
 */
export const isToday = (timestamp: number): boolean => {
  return dayjs(timestamp).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param timestamp 时间戳
 * @returns 是否为昨天
 */
export const isYesterday = (timestamp: number): boolean => {
  return dayjs(timestamp).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为本年
 * @param timestamp 时间戳
 * @returns 是否为本年
 */
export const isThisYear = (timestamp: number): boolean => {
  return dayjs(timestamp).isSame(dayjs(), 'year')
}