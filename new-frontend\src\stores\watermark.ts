// 水印状态管理
// 作者：仕伟
// 管理系统水印的显示配置和状态

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface WatermarkConfig {
  /** 是否显示水印 */
  show: boolean
  /** 水印文本 */
  text: string
  /** 透明度 0-1 */
  opacity: number
  /** 位置 */
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center'
  /** 字体大小 */
  fontSize: number
  /** 旋转角度 */
  rotation: number
}

export const useWatermarkStore = defineStore('watermark', () => {
  // 水印配置
  const config = ref<WatermarkConfig>({
    show: true,
    text: '兴隆县清水河主要支流水生态环境治理工程',
    opacity: 0.15,
    position: 'bottom-right',
    fontSize: 16,
    rotation: -15
  })

  // 计算属性
  const isVisible = computed(() => config.value.show)
  const watermarkText = computed(() => config.value.text)
  const opacity = computed(() => config.value.opacity)
  const position = computed(() => config.value.position)
  const fontSize = computed(() => config.value.fontSize)
  const rotation = computed(() => config.value.rotation)

  // 更新水印配置
  const updateConfig = (newConfig: Partial<WatermarkConfig>) => {
    config.value = { ...config.value, ...newConfig }
    saveToLocalStorage()
  }

  // 切换水印显示状态
  const toggleVisibility = () => {
    config.value.show = !config.value.show
    saveToLocalStorage()
  }

  // 设置水印文本
  const setText = (text: string) => {
    config.value.text = text
    saveToLocalStorage()
  }

  // 设置透明度
  const setOpacity = (opacity: number) => {
    config.value.opacity = Math.max(0.05, Math.min(0.5, opacity))
    saveToLocalStorage()
  }

  // 设置位置
  const setPosition = (position: WatermarkConfig['position']) => {
    config.value.position = position
    saveToLocalStorage()
  }

  // 设置字体大小
  const setFontSize = (fontSize: number) => {
    config.value.fontSize = Math.max(10, Math.min(24, fontSize))
    saveToLocalStorage()
  }

  // 设置旋转角度
  const setRotation = (rotation: number) => {
    config.value.rotation = rotation
    saveToLocalStorage()
  }

  // 重置为默认配置
  const resetToDefault = () => {
    config.value = {
      show: true,
      text: '兴隆县清水河主要支流水生态环境治理工程',
      opacity: 0.15,
      position: 'bottom-right',
      fontSize: 14,
      rotation: -15
    }
    saveToLocalStorage()
  }

  // 保存到本地存储
  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('watermark_config', JSON.stringify(config.value))
    } catch (error) {
      console.warn('保存水印配置到本地存储失败:', error)
    }
  }

  // 从本地存储加载
  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('watermark_config')
      if (saved) {
        const savedConfig = JSON.parse(saved)
        config.value = { ...config.value, ...savedConfig }
      }
    } catch (error) {
      console.warn('从本地存储加载水印配置失败:', error)
    }
  }

  // 获取预设配置
  const getPresetConfigs = () => [
    {
      name: '默认配置',
      config: {
        show: true,
        text: '兴隆县清水河主要支流水生态环境治理工程',
        opacity: 0.15,
        position: 'bottom-right' as const,
        fontSize: 14,
        rotation: -15
      }
    },
    {
      name: '低调模式',
      config: {
        show: true,
        text: '水生态环境治理工程',
        opacity: 0.08,
        position: 'bottom-left' as const,
        fontSize: 12,
        rotation: 0
      }
    },
    {
      name: '醒目模式',
      config: {
        show: true,
        text: '兴隆县清水河治理工程',
        opacity: 0.25,
        position: 'center' as const,
        fontSize: 16,
        rotation: -30
      }
    }
  ]

  return {
    // 状态
    config,
    
    // 计算属性
    isVisible,
    watermarkText,
    opacity,
    position,
    fontSize,
    rotation,

    // 方法
    updateConfig,
    toggleVisibility,
    setText,
    setOpacity,
    setPosition,
    setFontSize,
    setRotation,
    resetToDefault,
    loadFromLocalStorage,
    getPresetConfigs
  }
})