<template>
  <!-- 全新启动页 UI：星轨环形进度 + 阶段时间轴 + 响应式布局 -->
  <div class="bootx">
    <!-- 顶部栏：品牌与版本信息 -->
    <header class="bootx__top">
      <div class="brand">
        <img src="/logo.svg" alt="logo" />
        <div class="brand__text">
          <h1>河北省采砂监管一体机</h1>
          <p>Sand Mining Integrated Terminal</p>
        </div>
      </div>
      <div class="company">研发单位：河北小度科技有限公司</div>
      <div class="meta">
        <div class="meta__time">{{ nowTime }}</div>
        <div class="meta__ver">v2.0.0</div>
      </div>
    </header>

    <!-- 主体：左时间轴 + 右环形进度画布 -->
    <main class="bootx__main">
      <!-- 左侧：阶段时间轴 -->
      <section class="timeline">
        <div 
          v-for="(s, i) in stages" 
          :key="s.id" 
          class="timeline__item" 
          :class="[`is-${s.state}`]"
        >
          <div class="dot">
            <span class="dot__inner"></span>
          </div>
          <div class="content">
            <div class="content__head">
              <h3 class="title">{{ s.title }}</h3>
              <el-tag size="small" :type="s.state==='done'?'success':(s.state==='active'?'warning':'info')">
                {{ s.state === 'done' ? '完成' : s.state === 'active' ? '进行中' : '等待' }}
              </el-tag>
            </div>
            <p class="desc">{{ s.desc }}</p>
          </div>
          <div class="line" v-if="i < stages.length - 1"></div>
        </div>
      </section>

      <!-- 右侧：星轨环形进度 -->
      <section class="orbital">
        <div class="orbital__ring">
          <svg viewBox="0 0 200 200" class="ring">
            <defs>
              <linearGradient id="g1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stop-color="#409EFF"/>
                <stop offset="100%" stop-color="#7a5cff"/>
              </linearGradient>
            </defs>
            <circle cx="100" cy="100" r="76" class="track"/>
            <circle 
              cx="100" cy="100" r="76" 
              class="bar" 
              :style="{ strokeDasharray: dash + ', 999', stroke: 'url(#g1)' }"
            />
          </svg>
          <div class="orbital__center">
            <div class="percent">{{ Math.round(progress) }}%</div>
            <div class="status">{{ statusText }}</div>
          </div>
          <div class="stars">
            <span class="s s1"></span>
            <span class="s s2"></span>
            <span class="s s3"></span>
          </div>
        </div>
        <div class="tips">
          <el-tag size="small" type="info">{{ tipText }}</el-tag>
        </div>
        <div class="actions">
          <el-button 
            type="primary" 
            size="large"
            :disabled="!ready"
            :loading="!ready"
            @click="enter"
          >进入系统</el-button>
        </div>
      </section>
    </main>

    <!-- 底部：版权与工程名水印 -->
    <footer class="bootx__foot">
      <span>兴隆县清水河主要支流水生态环境治理工程</span>
      <span>© {{ new Date().getFullYear() }} 采砂一体机</span>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 全新启动界面实现
// 作者：仕伟

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 当前时间（数字时钟）
const nowTime = ref('')
let timeTimer: number | null = null

// 启动阶段列表（语义化阶段）
type StageState = 'pending' | 'active' | 'done'
interface StageItem {
  id: number
  title: string
  desc: string
  state: StageState
}

const stages = ref<StageItem[]>([
  { id: 1, title: '加载配置', desc: '读取项目、视频流、串口等关键参数', state: 'pending' },
  { id: 2, title: '检查设备', desc: '称重、摄像头、道闸等硬件自检', state: 'pending' },
  { id: 3, title: '建立通道', desc: '连接 WebSocket/MQTT 消息链路', state: 'pending' },
  { id: 4, title: '准备工作台', desc: '加载终端资源与缓存，优化首屏体验', state: 'pending' },
])

// 进度与状态
const progress = ref(0)
const ready = ref(false)
const dash = computed(() => (2 * Math.PI * 76) * (progress.value / 100))
const statusText = computed(() => {
  if (progress.value < 30) return '初始化中'
  if (progress.value < 60) return '加载组件'
  if (progress.value < 90) return '自检与连接'
  return ready.value ? '系统就绪' : '收尾中'
})
const tipText = computed(() => {
  const act = stages.value.find(s => s.state === 'active')
  return act ? `${act.title} · ${act.desc}` : '等待启动'
})

// 启动流程：依次激活阶段并推进进度
const start = async () => {
  for (let i = 0; i < stages.value.length; i++) {
    // 激活当前阶段
    stages.value[i].state = 'active'
    // 每阶段推进 25% 左右
    const target = (i + 1) * (100 / stages.value.length)
    while (progress.value < target) {
      progress.value = Math.min(target, progress.value + Math.max(1, (target - progress.value) * 0.18))
      await sleep(30)
    }
    // 阶段完成
    stages.value[i].state = 'done'
    await sleep(120)
  }
  // 完成并允许进入
  progress.value = 100
  ready.value = true
  // 自动进入（延迟）
  await sleep(1000)
  enter()
}

const enter = () => {
  router.push('/terminal')
}

const tickTime = () => {
  const d = new Date()
  const pad = (n: number) => (n < 10 ? '0' + n : '' + n)
  nowTime.value = `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
}

const sleep = (ms: number) => new Promise<void>(r => setTimeout(r, ms))

onMounted(() => {
  tickTime()
  timeTimer = window.setInterval(tickTime, 1000)
  start()
})

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer)
})
</script>

<style scoped lang="scss">
/* 自适应全屏容器 */
.bootx {
  min-height: 100dvh;
  display: grid;
  grid-template-rows: auto 1fr auto;
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* 顶部栏 */
.bootx__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: clamp(12px, 2vw, 20px) clamp(16px, 3vw, 28px);
  border-bottom: 1px solid var(--border-base);
  background: var(--bg-header);

  .brand {
    display: flex; align-items: center; gap: 10px;
    img { width: 28px; height: 28px; }
    &__text {
      h1 { margin: 0; font-size: clamp(16px, 1.6vw, 22px); font-weight: 700; color: var(--text-primary); }
      p { margin: 0; font-size: 12px; color: var(--text-secondary); }
    }
  }
  .company { color: var(--text-regular); font-size: 13px; }
  .meta {
    display: flex; align-items: baseline; gap: 10px;
    &__time { font-family: 'Courier New', monospace; font-size: clamp(14px, 1.4vw, 18px); color: var(--text-primary); }
    &__ver { font-size: 12px; color: var(--text-secondary); }
  }
}

/* 主体布局 */
.bootx__main {
  display: grid;
  grid-template-columns: minmax(320px, 520px) 1fr;
  gap: clamp(16px, 3vw, 28px);
  padding: clamp(16px, 3vw, 28px);
}

/* 左：时间轴 */
.timeline {
  position: relative;
  border: 1px solid var(--border-base);
  border-radius: 12px;
  background: var(--bg-card);
  backdrop-filter: none;
  padding: clamp(12px, 2vw, 18px);
  display: flex; flex-direction: column; gap: 16px;

  &__item {
    position: relative; display: grid; grid-template-columns: 18px 1fr; gap: 10px;
    .dot { position: relative; width: 18px; height: 18px; margin-top: 2px; }
    .dot__inner { display: block; width: 100%; height: 100%; border-radius: 50%; background: var(--border-light); }
    .content { display: grid; gap: 4px; }
    .content__head { display: flex; align-items: center; gap: 8px; }
    .title { margin: 0; font-size: 14px; font-weight: 600; color: var(--text-primary); }
    .desc { margin: 0; font-size: 12px; color: var(--text-regular); }
    .line { position: absolute; left: 8px; top: 22px; bottom: -14px; width: 2px; background: var(--border-extra-light); }

    &.is-active .dot__inner { background: var(--el-color-primary); box-shadow: 0 0 0 6px rgba(64,158,255,.15); }
    &.is-done .dot__inner { background: var(--el-color-success); box-shadow: 0 0 0 6px rgba(103,194,58,.15); }
  }
}

/* 右：环形进度与星轨动画 */
.orbital {
  border: 1px solid var(--border-base);
  border-radius: 16px;
  background: var(--bg-card);
  position: relative;
  display: grid; gap: 16px; align-content: center; justify-items: center;
  padding: clamp(16px, 3vw, 28px);

  &__ring { position: relative; width: min(420px, 60vw); aspect-ratio: 1/1; }
  .ring { width: 100%; height: 100%; }
  .track { fill: none; stroke: var(--border-light); stroke-width: 10; }
  .bar { fill: none; stroke-width: 10; stroke-linecap: round; transition: stroke-dasharray .2s ease; }
  &__center { position: absolute; inset: 0; display: grid; place-items: center; gap: 6px; }
  .percent { font-size: clamp(22px, 4vw, 36px); font-weight: 700; letter-spacing: 1px; color: var(--text-primary); }
  .status { font-size: 12px; color: var(--text-secondary); }

  .stars { position: absolute; inset: 0; pointer-events: none; }
  .s { position: absolute; width: 4px; height: 4px; background: #fff; border-radius: 50%; opacity: .7; filter: blur(1px); }
  .s1 { top: 8%; left: 14%; animation: drift 6s linear infinite; }
  .s2 { top: 78%; left: 72%; animation: drift 7.2s linear infinite; }
  .s3 { top: 40%; left: 88%; animation: drift 5.6s linear infinite; }

  .tips { opacity: .9; }
  .actions { margin-top: 6px; }
}

/* 底部 */
.bootx__foot {
  display: flex; align-items: center; justify-content: space-between;
  padding: clamp(10px, 2vw, 16px) clamp(16px, 3vw, 28px);
  border-top: 1px solid var(--border-base);
  color: var(--text-secondary); font-size: 12px;
}

/* 响应式：中小屏单列布局 */
@media (max-width: 1100px) {
  .bootx__main { grid-template-columns: 1fr; }
}

/* 动画 */
@keyframes drift { 
  0% { transform: translate(0, 0); opacity: .8 }
  50% { transform: translate(6px, -4px); opacity: .6 }
  100% { transform: translate(0, 0); opacity: .8 }
}

/* 小屏时公司信息自动换行居中，避免遮挡 */
@media (max-width: 640px) {
  .bootx__top { flex-wrap: wrap; gap: 8px; }
  .company { order: 3; width: 100%; text-align: center; }
}
</style>
