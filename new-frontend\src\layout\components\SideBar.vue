<template>
  <aside class="sidebar" :class="{ 'collapsed': collapsed }">
    <div class="sidebar-content">
      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        background-color="var(--bg-sidebar)"
        text-color="rgba(255, 255, 255, 0.8)"
        active-text-color="#fff"
        @select="handleMenuSelect"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
          :disabled="route.disabled"
        >
          <el-icon><component :is="route.meta?.icon" /></el-icon>
          <template #title>{{ route.meta?.title }}</template>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 收起/展开按钮 -->
      <el-button
        :icon="collapsed ? 'Expand' : 'Fold'"
        circle
        size="small"
        @click="handleToggle"
        class="toggle-btn"
      />
    </div>
  </aside>
</template>

<script setup lang="ts">
// 侧边栏组件
// 作者：仕伟

import { useRouter, useRoute } from 'vue-router'

interface Props {
  collapsed: boolean
}

interface Emits {
  (e: 'toggle'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const route = useRoute()

// 获取菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  return routes
    .filter(route => route.path !== '/' && route.path !== '/loading' && !route.path.includes('*'))
    .filter(route => route.meta?.title)
    .map(route => ({
      path: route.path,
      meta: route.meta,
      disabled: route.meta?.disabled as boolean
    }))
})

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 菜单选择处理
const handleMenuSelect = (path: string) => {
  if (path !== route.path) {
    router.push(path)
  }
}

// 切换侧边栏
const handleToggle = () => {
  emit('toggle')
}
</script>

<style scoped lang="scss">
.sidebar {
  width: 200px;
  background: var(--bg-sidebar);
  border-right: 1px solid var(--border-base);
  display: flex;
  flex-direction: column;
  transition: var(--transition-base);
  position: fixed;
  left: 0;
  top: 64px;
  bottom: 0;
  z-index: 999;

  &.collapsed {
    width: 64px;
  }

  @media (max-width: $breakpoint-md) {
    transform: translateX(-100%);

    &.mobile-open {
      transform: translateX(0);
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;

    :deep(.el-menu) {
      border-right: none;
      background: transparent !important;

      .el-menu-item {
        height: 56px;
        line-height: 56px;
        margin: 4px 8px;
        border-radius: var(--border-radius-base);
        transition: var(--transition-base);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
        }

        &.is-active {
          background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3)) !important;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #fff;
            border-radius: 0 2px 2px 0;
          }
        }

        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  .sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .toggle-btn {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: #fff;
      }
    }
  }
}
</style>