<template>
  <header class="header-bar">
    <div class="header-left">
      <!-- Logo 和标题 -->
      <div class="logo-section">
        <img src="/logo.svg" alt="Logo" class="logo" />
        <h1 class="title">河北省采(弃)砂监管一体机系统</h1>
      </div>
    </div>

    <div class="header-center">
      <!-- 实时状态指示器 -->
      <div class="status-indicators">
        <StatusIndicator 
          :status="deviceStatus.camera"
          label="摄像头"
          icon="VideoCamera"
        />
        <StatusIndicator 
          :status="deviceStatus.scale"
          label="地磅"
          icon="Scale"
        />
        <StatusIndicator 
          :status="deviceStatus.gate"
          label="道闸"
          icon="Operation"
        />
      </div>
    </div>

    <div class="header-right">
      <!-- 工具栏 -->
      <div class="toolbar">
        <!-- 全屏按钮 -->
        <el-tooltip content="全屏" placement="bottom">
          <el-button 
            :icon="isFullscreen ? 'Aim' : 'FullScreen'"
            circle
            @click="toggleFullscreen"
          />
        </el-tooltip>

        <!-- 刷新按钮 -->
        <el-tooltip content="刷新页面" placement="bottom">
          <el-button 
            icon="Refresh"
            circle
            :loading="refreshing"
            @click="handleRefresh"
          />
        </el-tooltip>

        <!-- 主题切换 -->
        <ThemeToggle />

        <!-- 系统时间 -->
        <div class="system-time">
          <div class="time">{{ currentTime }}</div>
          <div class="date">{{ currentDate }}</div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
// 头部导航栏组件
// 作者：仕伟

import StatusIndicator from '@/components/StatusIndicator.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { useDeviceStore } from '@/stores/device'
import { useFullscreen } from '@vueuse/core'
import dayjs from 'dayjs'

const deviceStore = useDeviceStore()
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen()

// 设备状态
const deviceStatus = computed(() => deviceStore.status)

// 刷新功能
const refreshing = ref(false)
const handleRefresh = async () => {
  refreshing.value = true
  try {
    // 这里可以触发全局数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('页面刷新成功')
  } finally {
    refreshing.value = false
  }
}

// 系统时间
const currentTime = ref('')
const currentDate = ref('')

const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY年MM月DD日')
}

// 初始化时间并设置定时器
onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped lang="scss">
.header-bar {
  height: 64px;
  background: var(--bg-header);
  border-bottom: 1px solid var(--border-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  box-shadow: var(--shadow-base);
  position: relative;
  z-index: 1000;

  .header-left {
    flex: 0 0 auto;

    .logo-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .logo {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius-base);
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        white-space: nowrap;

        @media (max-width: $breakpoint-md) {
          display: none;
        }
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .status-indicators {
      display: flex;
      gap: var(--spacing-lg);

      @media (max-width: $breakpoint-md) {
        gap: var(--spacing-sm);
      }
    }
  }

  .header-right {
    flex: 0 0 auto;

    .toolbar {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .system-time {
        margin-left: var(--spacing-md);
        text-align: right;
        user-select: none;

        .time {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
        }

        .date {
          font-size: 12px;
          color: var(--text-secondary);
          margin-top: 2px;
        }

        @media (max-width: $breakpoint-md) {
          display: none;
        }
      }
    }
  }
}
</style>